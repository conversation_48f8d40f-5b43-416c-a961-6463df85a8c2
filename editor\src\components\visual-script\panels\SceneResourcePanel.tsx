/**
 * 场景与资源管理面板组件
 * 集成批次2：显示和管理55个场景与资源管理节点
 * 包括场景编辑（15个）、场景管理（7个）、视口操作（8个）、资源加载（13个）、资源优化（9个）、场景过渡（1个）、场景生成（2个）
 */

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  Card,
  Collapse,
  List,
  Tag,
  Space,
  Button,
  Tooltip,
  Badge,
  Typography,
  Divider,
  Empty,
  Spin,
  Input,
  Select,
  Row,
  Col
} from 'antd';
import {
  ScissorOutlined,
  FolderOutlined,
  EyeOutlined,
  CloudDownloadOutlined,
  ThunderboltOutlined,
  SwapOutlined,
  BuildOutlined,
  InfoCircleOutlined,
  PlusOutlined,
  SearchOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { DraggableSceneResourceNode } from '../nodes/SceneResourceNodeDragDrop';

const { Panel } = Collapse;
const { Text, Title } = Typography;
const { Search } = Input;
const { Option } = Select;

// 场景与资源管理节点分类
export enum SceneResourceNodeCategory {
  SCENE_EDITING = 'scene_editing',
  SCENE_MANAGEMENT = 'scene_management', 
  VIEWPORT_OPERATIONS = 'viewport_operations',
  RESOURCE_LOADING = 'resource_loading',
  RESOURCE_OPTIMIZATION = 'resource_optimization',
  SCENE_TRANSITION = 'scene_transition',
  SCENE_GENERATION = 'scene_generation'
}

// 节点复杂度
export type NodeComplexity = 'basic' | 'intermediate' | 'advanced';

// 场景与资源管理节点项接口
export interface SceneResourceNodeItem {
  id: string;
  name: string;
  type: string;
  category: SceneResourceNodeCategory;
  description: string;
  complexity: NodeComplexity;
  tags: string[];
  inputs?: Array<{
    name: string;
    type: string;
    description: string;
    required?: boolean;
    defaultValue?: any;
  }>;
  outputs?: Array<{
    name: string;
    type: string;
    description: string;
  }>;
  properties?: Array<{
    name: string;
    type: string;
    description: string;
    defaultValue?: any;
    options?: any[];
  }>;
}

// 分类信息映射
const SCENE_RESOURCE_CATEGORY_MAP = {
  [SceneResourceNodeCategory.SCENE_EDITING]: {
    name: '场景编辑',
    icon: <ScissorOutlined />,
    color: '#1890ff',
    description: '场景编辑和对象操作功能'
  },
  [SceneResourceNodeCategory.SCENE_MANAGEMENT]: {
    name: '场景管理',
    icon: <FolderOutlined />,
    color: '#52c41a',
    description: '场景生命周期管理功能'
  },
  [SceneResourceNodeCategory.VIEWPORT_OPERATIONS]: {
    name: '视口操作',
    icon: <EyeOutlined />,
    color: '#722ed1',
    description: '视口控制和导航功能'
  },
  [SceneResourceNodeCategory.RESOURCE_LOADING]: {
    name: '资源加载',
    icon: <CloudDownloadOutlined />,
    color: '#fa8c16',
    description: '资源加载和管理功能'
  },
  [SceneResourceNodeCategory.RESOURCE_OPTIMIZATION]: {
    name: '资源优化',
    icon: <ThunderboltOutlined />,
    color: '#eb2f96',
    description: '资源优化和性能提升功能'
  },
  [SceneResourceNodeCategory.SCENE_TRANSITION]: {
    name: '场景过渡',
    icon: <SwapOutlined />,
    color: '#13c2c2',
    description: '场景间过渡效果功能'
  },
  [SceneResourceNodeCategory.SCENE_GENERATION]: {
    name: '场景生成',
    icon: <BuildOutlined />,
    color: '#f5222d',
    description: '程序化场景生成功能'
  }
};

// 复杂度颜色映射
const COMPLEXITY_COLORS = {
  basic: '#52c41a',
  intermediate: '#fa8c16', 
  advanced: '#f5222d'
};

// 组件属性接口
interface SceneResourcePanelProps {
  visible?: boolean;
  onNodeSelect?: (node: SceneResourceNodeItem) => void;
  onNodeAdd?: (nodeType: string) => void;
  height?: number | string;
  width?: number | string;
}

/**
 * 场景与资源管理面板组件
 */
const SceneResourcePanel: React.FC<SceneResourcePanelProps> = ({
  visible = true,
  onNodeSelect,
  onNodeAdd,
  height = '100%',
  width = '100%'
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [expandedPanels, setExpandedPanels] = useState<string[]>(['scene_editing']);
  const [filteredNodes, setFilteredNodes] = useState<SceneResourceNodeItem[]>([]);

  // 场景编辑节点（15个）
  const sceneEditingNodes = useMemo(() => [
    {
      id: 'create-scene',
      name: '创建场景',
      type: 'CreateScene',
      category: SceneResourceNodeCategory.SCENE_EDITING,
      description: '创建新的3D场景',
      complexity: 'basic' as NodeComplexity,
      tags: ['场景', '创建', '基础']
    },
    {
      id: 'load-scene',
      name: '加载场景',
      type: 'LoadScene',
      category: SceneResourceNodeCategory.SCENE_EDITING,
      description: '从文件加载场景数据',
      complexity: 'basic' as NodeComplexity,
      tags: ['场景', '加载', '文件']
    },
    {
      id: 'save-scene',
      name: '保存场景',
      type: 'SaveScene',
      category: SceneResourceNodeCategory.SCENE_EDITING,
      description: '将场景数据保存到文件',
      complexity: 'basic' as NodeComplexity,
      tags: ['场景', '保存', '文件']
    },
    {
      id: 'scene-hierarchy',
      name: '场景层级',
      type: 'SceneHierarchy',
      category: SceneResourceNodeCategory.SCENE_EDITING,
      description: '管理场景对象层级关系',
      complexity: 'intermediate' as NodeComplexity,
      tags: ['场景', '层级', '管理']
    },
    {
      id: 'select-object',
      name: '选择对象',
      type: 'SelectObject',
      category: SceneResourceNodeCategory.SCENE_EDITING,
      description: '在场景中选择对象',
      complexity: 'basic' as NodeComplexity,
      tags: ['对象', '选择', '交互']
    },
    {
      id: 'transform-gizmo',
      name: '变换控制器',
      type: 'TransformGizmo',
      category: SceneResourceNodeCategory.SCENE_EDITING,
      description: '提供3D变换控制器',
      complexity: 'intermediate' as NodeComplexity,
      tags: ['变换', '控制器', '3D']
    },
    {
      id: 'grid-snap',
      name: '网格吸附',
      type: 'GridSnap',
      category: SceneResourceNodeCategory.SCENE_EDITING,
      description: '启用网格吸附功能',
      complexity: 'basic' as NodeComplexity,
      tags: ['网格', '吸附', '对齐']
    },
    {
      id: 'duplicate-object',
      name: '复制对象',
      type: 'DuplicateObject',
      category: SceneResourceNodeCategory.SCENE_EDITING,
      description: '复制场景中的对象',
      complexity: 'basic' as NodeComplexity,
      tags: ['对象', '复制', '克隆']
    },
    {
      id: 'group-objects',
      name: '组合对象',
      type: 'GroupObjects',
      category: SceneResourceNodeCategory.SCENE_EDITING,
      description: '将多个对象组合为一个组',
      complexity: 'intermediate' as NodeComplexity,
      tags: ['对象', '组合', '分组']
    },
    {
      id: 'ungroup-objects',
      name: '取消组合',
      type: 'UngroupObjects',
      category: SceneResourceNodeCategory.SCENE_EDITING,
      description: '取消对象组合',
      complexity: 'intermediate' as NodeComplexity,
      tags: ['对象', '取消组合', '分离']
    },
    {
      id: 'align-objects',
      name: '对齐对象',
      type: 'AlignObjects',
      category: SceneResourceNodeCategory.SCENE_EDITING,
      description: '对齐多个选中对象',
      complexity: 'intermediate' as NodeComplexity,
      tags: ['对象', '对齐', '排列']
    },
    {
      id: 'distribute-objects',
      name: '分布对象',
      type: 'DistributeObjects',
      category: SceneResourceNodeCategory.SCENE_EDITING,
      description: '均匀分布多个对象',
      complexity: 'intermediate' as NodeComplexity,
      tags: ['对象', '分布', '排列']
    },
    {
      id: 'lock-object',
      name: '锁定对象',
      type: 'LockObject',
      category: SceneResourceNodeCategory.SCENE_EDITING,
      description: '锁定对象防止编辑',
      complexity: 'basic' as NodeComplexity,
      tags: ['对象', '锁定', '保护']
    },
    {
      id: 'hide-object',
      name: '隐藏对象',
      type: 'HideObject',
      category: SceneResourceNodeCategory.SCENE_EDITING,
      description: '隐藏场景中的对象',
      complexity: 'basic' as NodeComplexity,
      tags: ['对象', '隐藏', '可见性']
    },
    {
      id: 'focus-object',
      name: '聚焦对象',
      type: 'FocusObject',
      category: SceneResourceNodeCategory.SCENE_EDITING,
      description: '将视图聚焦到指定对象',
      complexity: 'basic' as NodeComplexity,
      tags: ['对象', '聚焦', '视图']
    }
  ], []);

  // 场景管理节点（7个）
  const sceneManagementNodes = useMemo(() => [
    {
      id: 'scene-manager',
      name: '场景管理器',
      type: 'SceneManager',
      category: SceneResourceNodeCategory.SCENE_MANAGEMENT,
      description: '管理多个场景',
      complexity: 'advanced' as NodeComplexity,
      tags: ['场景', '管理器', '多场景']
    },
    {
      id: 'active-scene',
      name: '活动场景',
      type: 'ActiveScene',
      category: SceneResourceNodeCategory.SCENE_MANAGEMENT,
      description: '设置当前活动场景',
      complexity: 'intermediate' as NodeComplexity,
      tags: ['场景', '活动', '切换']
    },
    {
      id: 'scene-metadata',
      name: '场景元数据',
      type: 'SceneMetadata',
      category: SceneResourceNodeCategory.SCENE_MANAGEMENT,
      description: '管理场景元信息',
      complexity: 'intermediate' as NodeComplexity,
      tags: ['场景', '元数据', '信息']
    },
    {
      id: 'scene-validation',
      name: '场景验证',
      type: 'SceneValidation',
      category: SceneResourceNodeCategory.SCENE_MANAGEMENT,
      description: '验证场景数据完整性',
      complexity: 'advanced' as NodeComplexity,
      tags: ['场景', '验证', '完整性']
    },
    {
      id: 'scene-optimization',
      name: '场景优化',
      type: 'SceneOptimization',
      category: SceneResourceNodeCategory.SCENE_MANAGEMENT,
      description: '优化场景性能',
      complexity: 'advanced' as NodeComplexity,
      tags: ['场景', '优化', '性能']
    },
    {
      id: 'scene-backup',
      name: '场景备份',
      type: 'SceneBackup',
      category: SceneResourceNodeCategory.SCENE_MANAGEMENT,
      description: '创建场景备份',
      complexity: 'intermediate' as NodeComplexity,
      tags: ['场景', '备份', '保护']
    },
    {
      id: 'scene-comparison',
      name: '场景比较',
      type: 'SceneComparison',
      category: SceneResourceNodeCategory.SCENE_MANAGEMENT,
      description: '比较不同版本场景',
      complexity: 'advanced' as NodeComplexity,
      tags: ['场景', '比较', '版本']
    }
  ], []);

  // 视口操作节点（8个）
  const viewportOperationNodes = useMemo(() => [
    {
      id: 'viewport-control',
      name: '视口控制',
      type: 'ViewportControl',
      category: SceneResourceNodeCategory.VIEWPORT_OPERATIONS,
      description: '控制3D视口',
      complexity: 'intermediate' as NodeComplexity,
      tags: ['视口', '控制', '3D']
    },
    {
      id: 'camera-orbit',
      name: '相机环绕',
      type: 'CameraOrbit',
      category: SceneResourceNodeCategory.VIEWPORT_OPERATIONS,
      description: '环绕目标旋转相机',
      complexity: 'intermediate' as NodeComplexity,
      tags: ['相机', '环绕', '旋转']
    },
    {
      id: 'camera-pan',
      name: '相机平移',
      type: 'CameraPan',
      category: SceneResourceNodeCategory.VIEWPORT_OPERATIONS,
      description: '平移相机视角',
      complexity: 'basic' as NodeComplexity,
      tags: ['相机', '平移', '移动']
    },
    {
      id: 'camera-zoom',
      name: '相机缩放',
      type: 'CameraZoom',
      category: SceneResourceNodeCategory.VIEWPORT_OPERATIONS,
      description: '缩放相机视野',
      complexity: 'basic' as NodeComplexity,
      tags: ['相机', '缩放', '视野']
    },
    {
      id: 'viewport-settings',
      name: '视口设置',
      type: 'ViewportSettings',
      category: SceneResourceNodeCategory.VIEWPORT_OPERATIONS,
      description: '配置视口显示设置',
      complexity: 'intermediate' as NodeComplexity,
      tags: ['视口', '设置', '配置']
    },
    {
      id: 'viewport-render-mode',
      name: '视口渲染模式',
      type: 'ViewportRenderMode',
      category: SceneResourceNodeCategory.VIEWPORT_OPERATIONS,
      description: '切换渲染模式',
      complexity: 'intermediate' as NodeComplexity,
      tags: ['视口', '渲染', '模式']
    },
    {
      id: 'viewport-grid',
      name: '视口网格',
      type: 'ViewportGrid',
      category: SceneResourceNodeCategory.VIEWPORT_OPERATIONS,
      description: '显示/隐藏网格',
      complexity: 'basic' as NodeComplexity,
      tags: ['视口', '网格', '显示']
    },
    {
      id: 'viewport-gizmos',
      name: '视口辅助线',
      type: 'ViewportGizmos',
      category: SceneResourceNodeCategory.VIEWPORT_OPERATIONS,
      description: '显示/隐藏辅助线',
      complexity: 'basic' as NodeComplexity,
      tags: ['视口', '辅助线', '显示']
    }
  ], []);

  // 资源加载节点（13个）
  const resourceLoadingNodes = useMemo(() => [
    {
      id: 'asset-loader',
      name: '资源加载器',
      type: 'AssetLoader',
      category: SceneResourceNodeCategory.RESOURCE_LOADING,
      description: '加载各种资源文件',
      complexity: 'intermediate' as NodeComplexity,
      tags: ['资源', '加载器', '文件']
    },
    {
      id: 'model-loader',
      name: '模型加载器',
      type: 'ModelLoader',
      category: SceneResourceNodeCategory.RESOURCE_LOADING,
      description: '加载3D模型文件',
      complexity: 'intermediate' as NodeComplexity,
      tags: ['模型', '加载', '3D']
    },
    {
      id: 'texture-loader',
      name: '纹理加载器',
      type: 'TextureLoader',
      category: SceneResourceNodeCategory.RESOURCE_LOADING,
      description: '加载纹理图片',
      complexity: 'basic' as NodeComplexity,
      tags: ['纹理', '加载', '图片']
    },
    {
      id: 'audio-loader',
      name: '音频加载器',
      type: 'AudioLoader',
      category: SceneResourceNodeCategory.RESOURCE_LOADING,
      description: '加载音频文件',
      complexity: 'basic' as NodeComplexity,
      tags: ['音频', '加载', '声音']
    },
    {
      id: 'animation-loader',
      name: '动画加载器',
      type: 'AnimationLoader',
      category: SceneResourceNodeCategory.RESOURCE_LOADING,
      description: '加载动画数据',
      complexity: 'intermediate' as NodeComplexity,
      tags: ['动画', '加载', '数据']
    },
    {
      id: 'font-loader',
      name: '字体加载器',
      type: 'FontLoader',
      category: SceneResourceNodeCategory.RESOURCE_LOADING,
      description: '加载字体文件',
      complexity: 'basic' as NodeComplexity,
      tags: ['字体', '加载', '文本']
    },
    {
      id: 'video-loader',
      name: '视频加载器',
      type: 'VideoLoader',
      category: SceneResourceNodeCategory.RESOURCE_LOADING,
      description: '加载视频文件',
      complexity: 'intermediate' as NodeComplexity,
      tags: ['视频', '加载', '媒体']
    },
    {
      id: 'script-loader',
      name: '脚本加载器',
      type: 'ScriptLoader',
      category: SceneResourceNodeCategory.RESOURCE_LOADING,
      description: '加载脚本文件',
      complexity: 'advanced' as NodeComplexity,
      tags: ['脚本', '加载', '代码']
    },
    {
      id: 'config-loader',
      name: '配置加载器',
      type: 'ConfigLoader',
      category: SceneResourceNodeCategory.RESOURCE_LOADING,
      description: '加载配置文件',
      complexity: 'intermediate' as NodeComplexity,
      tags: ['配置', '加载', '设置']
    },
    {
      id: 'data-loader',
      name: '数据加载器',
      type: 'DataLoader',
      category: SceneResourceNodeCategory.RESOURCE_LOADING,
      description: '加载数据文件',
      complexity: 'intermediate' as NodeComplexity,
      tags: ['数据', '加载', '文件']
    },
    {
      id: 'progressive-loader',
      name: '渐进加载器',
      type: 'ProgressiveLoader',
      category: SceneResourceNodeCategory.RESOURCE_LOADING,
      description: '实现渐进式加载',
      complexity: 'advanced' as NodeComplexity,
      tags: ['渐进', '加载', '优化']
    },
    {
      id: 'cache-loader',
      name: '缓存加载器',
      type: 'CacheLoader',
      category: SceneResourceNodeCategory.RESOURCE_LOADING,
      description: '管理资源缓存',
      complexity: 'advanced' as NodeComplexity,
      tags: ['缓存', '加载', '管理']
    },
    {
      id: 'streaming-loader',
      name: '流式加载器',
      type: 'StreamingLoader',
      category: SceneResourceNodeCategory.RESOURCE_LOADING,
      description: '实现流式资源加载',
      complexity: 'advanced' as NodeComplexity,
      tags: ['流式', '加载', '实时']
    }
  ], []);

  // 资源优化节点（9个）
  const resourceOptimizationNodes = useMemo(() => [
    {
      id: 'asset-optimizer',
      name: '资源优化器',
      type: 'AssetOptimizer',
      category: SceneResourceNodeCategory.RESOURCE_OPTIMIZATION,
      description: '优化资源文件大小',
      complexity: 'advanced' as NodeComplexity,
      tags: ['资源', '优化', '压缩']
    },
    {
      id: 'texture-compression',
      name: '纹理压缩',
      type: 'TextureCompression',
      category: SceneResourceNodeCategory.RESOURCE_OPTIMIZATION,
      description: '压缩纹理文件',
      complexity: 'advanced' as NodeComplexity,
      tags: ['纹理', '压缩', '优化']
    },
    {
      id: 'mesh-compression',
      name: '网格压缩',
      type: 'MeshCompression',
      category: SceneResourceNodeCategory.RESOURCE_OPTIMIZATION,
      description: '压缩3D网格数据',
      complexity: 'advanced' as NodeComplexity,
      tags: ['网格', '压缩', '3D']
    },
    {
      id: 'audio-compression',
      name: '音频压缩',
      type: 'AudioCompression',
      category: SceneResourceNodeCategory.RESOURCE_OPTIMIZATION,
      description: '压缩音频文件',
      complexity: 'intermediate' as NodeComplexity,
      tags: ['音频', '压缩', '优化']
    },
    {
      id: 'asset-bundle',
      name: '资源包',
      type: 'AssetBundle',
      category: SceneResourceNodeCategory.RESOURCE_OPTIMIZATION,
      description: '打包资源文件',
      complexity: 'advanced' as NodeComplexity,
      tags: ['资源', '打包', '合并']
    },
    {
      id: 'asset-cache',
      name: '资源缓存',
      type: 'AssetCache',
      category: SceneResourceNodeCategory.RESOURCE_OPTIMIZATION,
      description: '缓存常用资源',
      complexity: 'intermediate' as NodeComplexity,
      tags: ['资源', '缓存', '性能']
    },
    {
      id: 'asset-preload',
      name: '资源预加载',
      type: 'AssetPreload',
      category: SceneResourceNodeCategory.RESOURCE_OPTIMIZATION,
      description: '预加载重要资源',
      complexity: 'intermediate' as NodeComplexity,
      tags: ['资源', '预加载', '优化']
    },
    {
      id: 'asset-unload',
      name: '资源卸载',
      type: 'AssetUnload',
      category: SceneResourceNodeCategory.RESOURCE_OPTIMIZATION,
      description: '卸载不需要的资源',
      complexity: 'intermediate' as NodeComplexity,
      tags: ['资源', '卸载', '内存']
    },
    {
      id: 'memory-manager',
      name: '内存管理器',
      type: 'MemoryManager',
      category: SceneResourceNodeCategory.RESOURCE_OPTIMIZATION,
      description: '管理内存使用',
      complexity: 'advanced' as NodeComplexity,
      tags: ['内存', '管理', '优化']
    }
  ], []);

  // 场景过渡节点（1个）
  const sceneTransitionNodes = useMemo(() => [
    {
      id: 'scene-transition',
      name: '场景过渡',
      type: 'SceneTransition',
      category: SceneResourceNodeCategory.SCENE_TRANSITION,
      description: '实现场景间的平滑过渡',
      complexity: 'advanced' as NodeComplexity,
      tags: ['场景', '过渡', '动画']
    }
  ], []);

  // 场景生成节点（2个）
  const sceneGenerationNodes = useMemo(() => [
    {
      id: 'procedural-scene',
      name: '程序化场景',
      type: 'ProceduralScene',
      category: SceneResourceNodeCategory.SCENE_GENERATION,
      description: '程序化生成场景内容',
      complexity: 'advanced' as NodeComplexity,
      tags: ['程序化', '生成', '自动']
    },
    {
      id: 'template-scene',
      name: '模板场景',
      type: 'TemplateScene',
      category: SceneResourceNodeCategory.SCENE_GENERATION,
      description: '基于模板创建场景',
      complexity: 'intermediate' as NodeComplexity,
      tags: ['模板', '场景', '创建']
    }
  ], []);

  // 合并所有节点
  const allNodes = useMemo(() => [
    ...sceneEditingNodes,
    ...sceneManagementNodes,
    ...viewportOperationNodes,
    ...resourceLoadingNodes,
    ...resourceOptimizationNodes,
    ...sceneTransitionNodes,
    ...sceneGenerationNodes
  ], [
    sceneEditingNodes,
    sceneManagementNodes,
    viewportOperationNodes,
    resourceLoadingNodes,
    resourceOptimizationNodes,
    sceneTransitionNodes,
    sceneGenerationNodes
  ]);

  // 按分类分组节点
  const nodesByCategory = useMemo(() => {
    const grouped: { [key in SceneResourceNodeCategory]: SceneResourceNodeItem[] } = {
      [SceneResourceNodeCategory.SCENE_EDITING]: [],
      [SceneResourceNodeCategory.SCENE_MANAGEMENT]: [],
      [SceneResourceNodeCategory.VIEWPORT_OPERATIONS]: [],
      [SceneResourceNodeCategory.RESOURCE_LOADING]: [],
      [SceneResourceNodeCategory.RESOURCE_OPTIMIZATION]: [],
      [SceneResourceNodeCategory.SCENE_TRANSITION]: [],
      [SceneResourceNodeCategory.SCENE_GENERATION]: []
    };

    filteredNodes.forEach(node => {
      if (grouped[node.category]) {
        grouped[node.category].push(node);
      }
    });

    return grouped;
  }, [filteredNodes]);

  // 过滤节点
  useEffect(() => {
    let filtered = allNodes;

    // 按搜索文本过滤
    if (searchText) {
      filtered = filtered.filter(node =>
        node.name.toLowerCase().includes(searchText.toLowerCase()) ||
        node.description.toLowerCase().includes(searchText.toLowerCase()) ||
        node.tags.some(tag => tag.toLowerCase().includes(searchText.toLowerCase()))
      );
    }

    // 按分类过滤
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(node => node.category === selectedCategory);
    }

    setFilteredNodes(filtered);
  }, [allNodes, searchText, selectedCategory]);

  // 渲染节点列表项
  const renderNodeItem = useCallback((node: SceneResourceNodeItem) => (
    <DraggableSceneResourceNode
      key={node.id}
      node={node}
      onNodeSelect={onNodeSelect}
      onNodeAdd={onNodeAdd}
      onDragStart={(dragNode) => {
        console.log('开始拖拽节点:', dragNode.name);
      }}
      onDragEnd={(dragNode, didDrop) => {
        console.log('拖拽结束:', dragNode.name, '是否放置:', didDrop);
      }}
    />
  ), [onNodeSelect, onNodeAdd]);

  // 渲染分类面板
  const renderCategoryPanel = useCallback((category: SceneResourceNodeCategory) => {
    const categoryInfo = SCENE_RESOURCE_CATEGORY_MAP[category];
    const categoryNodes = nodesByCategory[category];

    if (categoryNodes.length === 0) {
      return null;
    }

    return (
      <Panel
        header={
          <Space>
            {categoryInfo.icon}
            <Text strong>{categoryInfo.name}</Text>
            <Badge count={categoryNodes.length} size="small" />
            <Tooltip title={categoryInfo.description}>
              <InfoCircleOutlined style={{ color: '#999' }} />
            </Tooltip>
          </Space>
        }
        key={category}
      >
        <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
          {categoryNodes.map(renderNodeItem)}
        </div>
      </Panel>
    );
  }, [nodesByCategory, renderNodeItem]);

  if (!visible) {
    return null;
  }

  return (
    <Card
      title={
        <Space>
          <FolderOutlined />
          <Title level={4} style={{ margin: 0 }}>
            场景与资源管理节点
          </Title>
          <Badge count={55} style={{ backgroundColor: '#52c41a' }} />
        </Space>
      }
      size="small"
      style={{ height, width, overflow: 'hidden' }}
      bodyStyle={{ padding: '12px', height: 'calc(100% - 57px)', overflow: 'auto' }}
    >
      <Spin spinning={loading}>
        {/* 搜索和过滤组件 */}
        <Row gutter={[8, 8]} style={{ marginBottom: '12px' }}>
          <Col span={16}>
            <Search
              placeholder="搜索节点..."
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              allowClear
              size="small"
            />
          </Col>
          <Col span={8}>
            <Select
              value={selectedCategory}
              onChange={setSelectedCategory}
              size="small"
              style={{ width: '100%' }}
            >
              <Option value="all">全部分类</Option>
              {Object.entries(SCENE_RESOURCE_CATEGORY_MAP).map(([key, info]) => (
                <Option key={key} value={key}>
                  {info.name}
                </Option>
              ))}
            </Select>
          </Col>
        </Row>

        <Divider style={{ margin: '8px 0' }} />

        {/* 统计信息 */}
        <Row gutter={[8, 8]} style={{ marginBottom: '12px' }}>
          <Col span={24}>
            <Space size="small">
              <Text type="secondary">
                显示 {filteredNodes.length} / {allNodes.length} 个节点
              </Text>
              <Divider type="vertical" />
              <Tag color="blue">场景编辑: {sceneEditingNodes.length}</Tag>
              <Tag color="green">场景管理: {sceneManagementNodes.length}</Tag>
              <Tag color="purple">视口操作: {viewportOperationNodes.length}</Tag>
              <Tag color="orange">资源加载: {resourceLoadingNodes.length}</Tag>
              <Tag color="red">资源优化: {resourceOptimizationNodes.length}</Tag>
            </Space>
          </Col>
        </Row>

        {/* 节点分类面板 */}
        {filteredNodes.length > 0 ? (
          <Collapse
            activeKey={expandedPanels}
            onChange={(keys) => setExpandedPanels(keys as string[])}
            size="small"
            ghost
          >
            {Object.values(SceneResourceNodeCategory).map(renderCategoryPanel)}
          </Collapse>
        ) : (
          <Empty
            description="没有找到匹配的节点"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        )}
      </Spin>
    </Card>
  );
};
};

export default SceneResourcePanel;
