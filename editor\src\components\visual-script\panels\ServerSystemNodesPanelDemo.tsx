/**
 * 服务器系统节点面板演示
 * 展示如何使用服务器系统节点面板组件
 */

import React, { useState } from 'react';
import { Card, Row, Col, Button, Space, message, Typography } from 'antd';
import { ServerOutlined, PlayCircleOutlined, InfoCircleOutlined } from '@ant-design/icons';
import ServerSystemNodesPanelWrapper from './ServerSystemNodesPanelWrapper';
import { ServerNodeItem } from './ServerSystemNodesPanel';

const { Title, Paragraph, Text } = Typography;

/**
 * 服务器系统节点面板演示组件
 */
const ServerSystemNodesPanelDemo: React.FC = () => {
  const [demoActive, setDemoActive] = useState(false);
  const [addedNodes, setAddedNodes] = useState<Array<{ type: string; data: any }>>([]);

  // 处理节点添加
  const handleNodeAdded = (nodeType: string, nodeData: any) => {
    setAddedNodes(prev => [...prev, { type: nodeType, data: nodeData }]);
    message.success(`成功添加 ${nodeType} 节点到画布`);
  };

  // 启动演示
  const startDemo = () => {
    setDemoActive(true);
    setAddedNodes([]);
    message.info('服务器系统节点面板演示已启动');
  };

  // 停止演示
  const stopDemo = () => {
    setDemoActive(false);
    message.info('演示已停止');
  };

  return (
    <div style={{ padding: '24px', height: '100vh', overflow: 'auto' }}>
      {/* 演示说明 */}
      <Card style={{ marginBottom: 16 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Title level={3}>
            <ServerOutlined /> 服务器系统节点面板演示
          </Title>
          
          <Paragraph>
            本演示展示了集成批次4：服务器系统面板（58个节点）的功能特性：
          </Paragraph>
          
          <ul>
            <li><Text strong>用户服务节点</Text>：用户注册、认证、权限管理等（12个节点）</li>
            <li><Text strong>数据服务节点</Text>：数据库连接、查询、缓存等（12个节点）</li>
            <li><Text strong>文件服务节点</Text>：文件上传、下载、存储等（10个节点）</li>
            <li><Text strong>认证授权节点</Text>：JWT、OAuth2、RBAC等（7个节点）</li>
            <li><Text strong>通知服务节点</Text>：邮件、推送、短信通知等（8个节点）</li>
            <li><Text strong>监控服务节点</Text>：系统监控、性能分析等（5个节点）</li>
            <li><Text strong>项目管理节点</Text>：项目管理、任务管理等（4个节点）</li>
          </ul>

          <Space>
            <Button
              type="primary"
              icon={<PlayCircleOutlined />}
              onClick={startDemo}
              disabled={demoActive}
            >
              启动演示
            </Button>
            <Button
              onClick={stopDemo}
              disabled={!demoActive}
            >
              停止演示
            </Button>
          </Space>
        </Space>
      </Card>

      {/* 演示面板 */}
      {demoActive && (
        <Row gutter={[16, 16]} style={{ height: 'calc(100vh - 300px)' }}>
          {/* 服务器节点面板 */}
          <Col span={18}>
            <ServerSystemNodesPanelWrapper
              visible={true}
              height="100%"
              onNodeAdded={handleNodeAdded}
            />
          </Col>

          {/* 演示信息面板 */}
          <Col span={6}>
            <Card
              title={
                <Space>
                  <InfoCircleOutlined />
                  <Text>演示信息</Text>
                </Space>
              }
              size="small"
              style={{ height: '100%' }}
            >
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <Text strong>功能特性：</Text>
                  <ul style={{ fontSize: '12px', marginTop: 8 }}>
                    <li>节点分类展示</li>
                    <li>搜索和过滤</li>
                    <li>拖拽添加节点</li>
                    <li>属性编辑界面</li>
                    <li>实时状态更新</li>
                  </ul>
                </div>

                <div>
                  <Text strong>操作指南：</Text>
                  <ol style={{ fontSize: '12px', marginTop: 8 }}>
                    <li>浏览不同分类的节点</li>
                    <li>使用搜索框查找节点</li>
                    <li>点击节点查看详情</li>
                    <li>点击"+"添加到画布</li>
                    <li>在右侧编辑节点属性</li>
                  </ol>
                </div>

                <div>
                  <Text strong>已添加节点：</Text>
                  <div style={{ maxHeight: 200, overflow: 'auto', marginTop: 8 }}>
                    {addedNodes.length === 0 ? (
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        暂无添加的节点
                      </Text>
                    ) : (
                      addedNodes.map((node, index) => (
                        <div key={index} style={{ fontSize: '12px', marginBottom: 4 }}>
                          <Text code>{node.type}</Text>
                        </div>
                      ))
                    )}
                  </div>
                </div>

                <div>
                  <Text strong>技术特点：</Text>
                  <ul style={{ fontSize: '12px', marginTop: 8 }}>
                    <li>React + TypeScript</li>
                    <li>Ant Design UI组件</li>
                    <li>React DnD拖拽</li>
                    <li>模块化设计</li>
                    <li>完整的测试覆盖</li>
                  </ul>
                </div>
              </Space>
            </Card>
          </Col>
        </Row>
      )}

      {/* 演示说明 */}
      {!demoActive && (
        <Card>
          <Space direction="vertical" style={{ width: '100%' }}>
            <Title level={4}>集成成果</Title>
            
            <Paragraph>
              ✅ <Text strong>集成批次4已完成</Text>：成功集成58个服务器系统节点到编辑器
            </Paragraph>

            <Row gutter={[16, 16]}>
              <Col span={8}>
                <Card size="small">
                  <Text strong>用户服务</Text>
                  <br />
                  <Text type="secondary">12个节点</Text>
                  <br />
                  <Text style={{ fontSize: '12px' }}>
                    用户注册、认证、权限管理
                  </Text>
                </Card>
              </Col>
              <Col span={8}>
                <Card size="small">
                  <Text strong>数据服务</Text>
                  <br />
                  <Text type="secondary">12个节点</Text>
                  <br />
                  <Text style={{ fontSize: '12px' }}>
                    数据库操作、缓存、备份
                  </Text>
                </Card>
              </Col>
              <Col span={8}>
                <Card size="small">
                  <Text strong>文件服务</Text>
                  <br />
                  <Text type="secondary">10个节点</Text>
                  <br />
                  <Text style={{ fontSize: '12px' }}>
                    文件上传、下载、存储
                  </Text>
                </Card>
              </Col>
            </Row>

            <Row gutter={[16, 16]}>
              <Col span={6}>
                <Card size="small">
                  <Text strong>认证授权</Text>
                  <br />
                  <Text type="secondary">7个节点</Text>
                </Card>
              </Col>
              <Col span={6}>
                <Card size="small">
                  <Text strong>通知服务</Text>
                  <br />
                  <Text type="secondary">8个节点</Text>
                </Card>
              </Col>
              <Col span={6}>
                <Card size="small">
                  <Text strong>监控服务</Text>
                  <br />
                  <Text type="secondary">5个节点</Text>
                </Card>
              </Col>
              <Col span={6}>
                <Card size="small">
                  <Text strong>项目管理</Text>
                  <br />
                  <Text type="secondary">4个节点</Text>
                </Card>
              </Col>
            </Row>
          </Space>
        </Card>
      )}
    </div>
  );
};

export default ServerSystemNodesPanelDemo;
