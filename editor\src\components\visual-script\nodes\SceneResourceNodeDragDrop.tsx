/**
 * 场景与资源管理节点拖拽组件
 * 实现节点从面板拖拽到编辑器画布的功能
 */

import React, { useCallback, useState } from 'react';
import { useDrag, useDrop } from 'react-dnd';
import { Card, Tag, Space, Typography, Tooltip, Button } from 'antd';
import { 
  PlusOutlined, 
  DragOutlined, 
  EyeOutlined,
  InfoCircleOutlined 
} from '@ant-design/icons';
import { SceneResourceNodeItem } from '../panels/SceneResourcePanel';

const { Text } = Typography;

// 拖拽项目类型
export const ItemTypes = {
  SCENE_RESOURCE_NODE: 'scene_resource_node'
};

// 拖拽数据接口
export interface DragItem {
  type: string;
  node: SceneResourceNodeItem;
}

// 拖拽预览组件属性
interface NodeDragPreviewProps {
  node: SceneResourceNodeItem;
  isDragging: boolean;
}

// 拖拽预览组件
const NodeDragPreview: React.FC<NodeDragPreviewProps> = ({ node, isDragging }) => (
  <Card
    size="small"
    style={{
      opacity: isDragging ? 0.5 : 1,
      transform: isDragging ? 'rotate(5deg)' : 'none',
      cursor: 'grabbing',
      minWidth: '200px',
      border: '2px dashed #1890ff'
    }}
  >
    <Space direction="vertical" size="small" style={{ width: '100%' }}>
      <Text strong>{node.name}</Text>
      <Text type="secondary" style={{ fontSize: '12px' }}>
        {node.description}
      </Text>
      <Space size="small">
        <Tag color="blue" size="small">{node.type}</Tag>
        <Tag color="green" size="small">{node.complexity}</Tag>
      </Space>
    </Space>
  </Card>
);

// 可拖拽节点组件属性
interface DraggableSceneResourceNodeProps {
  node: SceneResourceNodeItem;
  onNodeSelect?: (node: SceneResourceNodeItem) => void;
  onNodeAdd?: (nodeType: string) => void;
  onDragStart?: (node: SceneResourceNodeItem) => void;
  onDragEnd?: (node: SceneResourceNodeItem, didDrop: boolean) => void;
}

/**
 * 可拖拽的场景与资源管理节点组件
 */
export const DraggableSceneResourceNode: React.FC<DraggableSceneResourceNodeProps> = ({
  node,
  onNodeSelect,
  onNodeAdd,
  onDragStart,
  onDragEnd
}) => {
  const [isHovered, setIsHovered] = useState(false);

  // 配置拖拽
  const [{ isDragging }, drag, preview] = useDrag({
    type: ItemTypes.SCENE_RESOURCE_NODE,
    item: (): DragItem => {
      onDragStart?.(node);
      return { type: ItemTypes.SCENE_RESOURCE_NODE, node };
    },
    end: (item, monitor) => {
      const didDrop = monitor.didDrop();
      onDragEnd?.(node, didDrop);
    },
    collect: (monitor) => ({
      isDragging: monitor.isDragging()
    })
  });

  // 处理节点点击
  const handleNodeClick = useCallback(() => {
    onNodeSelect?.(node);
  }, [node, onNodeSelect]);

  // 处理添加节点
  const handleAddNode = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    onNodeAdd?.(node.type);
  }, [node.type, onNodeAdd]);

  // 获取复杂度颜色
  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'basic': return '#52c41a';
      case 'intermediate': return '#fa8c16';
      case 'advanced': return '#f5222d';
      default: return '#d9d9d9';
    }
  };

  return (
    <>
      {/* 拖拽预览 */}
      <div ref={preview} style={{ display: 'none' }}>
        <NodeDragPreview node={node} isDragging={isDragging} />
      </div>

      {/* 节点卡片 */}
      <Card
        ref={drag}
        size="small"
        hoverable
        style={{
          marginBottom: '8px',
          opacity: isDragging ? 0.5 : 1,
          cursor: isDragging ? 'grabbing' : 'grab',
          border: isHovered ? '1px solid #1890ff' : '1px solid #d9d9d9',
          transition: 'all 0.2s ease'
        }}
        bodyStyle={{ padding: '8px' }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onClick={handleNodeClick}
      >
        <Space direction="vertical" size="small" style={{ width: '100%' }}>
          {/* 节点标题行 */}
          <Space style={{ width: '100%', justifyContent: 'space-between' }}>
            <Space size="small">
              <DragOutlined style={{ color: '#999', fontSize: '12px' }} />
              <Text strong style={{ fontSize: '13px' }}>{node.name}</Text>
            </Space>
            <Space size="small">
              <Tooltip title="查看详情">
                <Button
                  type="text"
                  size="small"
                  icon={<EyeOutlined />}
                  onClick={(e) => {
                    e.stopPropagation();
                    onNodeSelect?.(node);
                  }}
                />
              </Tooltip>
              <Tooltip title="添加到画布">
                <Button
                  type="text"
                  size="small"
                  icon={<PlusOutlined />}
                  onClick={handleAddNode}
                />
              </Tooltip>
            </Space>
          </Space>

          {/* 节点描述 */}
          <Text 
            type="secondary" 
            style={{ 
              fontSize: '11px', 
              lineHeight: '1.3',
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical',
              overflow: 'hidden'
            }}
          >
            {node.description}
          </Text>

          {/* 节点标签 */}
          <Space size="small" wrap>
            <Tag 
              color="blue" 
              size="small"
              style={{ fontSize: '10px', margin: '1px' }}
            >
              {node.type}
            </Tag>
            <Tag 
              color={getComplexityColor(node.complexity)}
              size="small"
              style={{ fontSize: '10px', margin: '1px' }}
            >
              {node.complexity}
            </Tag>
            {node.tags.slice(0, 2).map(tag => (
              <Tag 
                key={tag}
                size="small"
                style={{ fontSize: '10px', margin: '1px' }}
              >
                {tag}
              </Tag>
            ))}
            {node.tags.length > 2 && (
              <Tooltip title={node.tags.slice(2).join(', ')}>
                <Tag size="small" style={{ fontSize: '10px', margin: '1px' }}>
                  +{node.tags.length - 2}
                </Tag>
              </Tooltip>
            )}
          </Space>
        </Space>
      </Card>
    </>
  );
};

// 拖拽目标区域组件属性
interface SceneResourceDropZoneProps {
  onNodeDrop?: (node: SceneResourceNodeItem, position: { x: number; y: number }) => void;
  children?: React.ReactNode;
}

/**
 * 场景与资源管理节点拖拽目标区域
 */
export const SceneResourceDropZone: React.FC<SceneResourceDropZoneProps> = ({
  onNodeDrop,
  children
}) => {
  const [{ isOver, canDrop }, drop] = useDrop({
    accept: ItemTypes.SCENE_RESOURCE_NODE,
    drop: (item: DragItem, monitor) => {
      const clientOffset = monitor.getClientOffset();
      if (clientOffset && onNodeDrop) {
        onNodeDrop(item.node, { x: clientOffset.x, y: clientOffset.y });
      }
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop()
    })
  });

  return (
    <div
      ref={drop}
      style={{
        width: '100%',
        height: '100%',
        backgroundColor: isOver && canDrop ? 'rgba(24, 144, 255, 0.1)' : 'transparent',
        border: isOver && canDrop ? '2px dashed #1890ff' : 'none',
        transition: 'all 0.2s ease'
      }}
    >
      {children}
    </div>
  );
};

export default DraggableSceneResourceNode;
