/**
 * 服务器系统节点面板包装器
 * 集成服务器系统面板到编辑器主界面
 */

import React, { useState, useCallback, useEffect } from 'react';
import { Card, Row, Col, message, Spin } from 'antd';
import { useTranslation } from 'react-i18next';
import ServerSystemNodesPanel, { ServerNodeItem } from './ServerSystemNodesPanel';
import ServerNodePropertyEditor from '../nodes/ServerNodePropertyEditor';
import { useEngineService } from '../../../hooks/useEngineService';

// 包装器组件属性
interface ServerSystemNodesPanelWrapperProps {
  visible?: boolean;
  height?: number | string;
  onNodeAdded?: (nodeType: string, nodeData: any) => void;
}

/**
 * 服务器系统节点面板包装器组件
 */
const ServerSystemNodesPanelWrapper: React.FC<ServerSystemNodesPanelWrapperProps> = ({
  visible = true,
  height = '100%',
  onNodeAdded
}) => {
  const { t } = useTranslation();
  const engineService = useEngineService();
  const [selectedNode, setSelectedNode] = useState<ServerNodeItem | undefined>();
  const [loading, setLoading] = useState(false);

  // 处理节点选择
  const handleNodeSelect = useCallback((node: ServerNodeItem) => {
    setSelectedNode(node);
    console.log('选择服务器节点:', node.name);
  }, []);

  // 处理节点添加
  const handleNodeAdd = useCallback(async (nodeType: string) => {
    try {
      setLoading(true);
      
      // 创建节点数据
      const nodeData = {
        type: nodeType,
        id: `${nodeType}_${Date.now()}`,
        position: { x: 100, y: 100 },
        properties: getDefaultNodeProperties(nodeType)
      };

      // 添加到引擎
      if (engineService) {
        await engineService.addVisualScriptNode(nodeData);
      }

      // 通知父组件
      if (onNodeAdded) {
        onNodeAdded(nodeType, nodeData);
      }

      message.success(`已添加 ${nodeType} 节点`);
      console.log('添加服务器节点:', nodeType, nodeData);
      
    } catch (error) {
      console.error('添加节点失败:', error);
      message.error('添加节点失败');
    } finally {
      setLoading(false);
    }
  }, [engineService, onNodeAdded]);

  // 处理属性变更
  const handlePropertyChange = useCallback((nodeId: string, property: string, value: any) => {
    console.log('属性变更:', nodeId, property, value);
    // 这里可以实时更新节点属性
  }, []);

  // 处理属性保存
  const handlePropertySave = useCallback(async (nodeId: string, properties: Record<string, any>) => {
    try {
      setLoading(true);
      
      // 保存到引擎
      if (engineService) {
        await engineService.updateNodeProperties(nodeId, properties);
      }

      message.success('属性保存成功');
      console.log('保存节点属性:', nodeId, properties);
      
    } catch (error) {
      console.error('保存属性失败:', error);
      message.error('保存属性失败');
    } finally {
      setLoading(false);
    }
  }, [engineService]);

  // 获取默认节点属性
  const getDefaultNodeProperties = (nodeType: string): Record<string, any> => {
    const commonProperties = {
      enabled: true,
      timeout: 30000,
      retryCount: 3,
      logLevel: 'info'
    };

    switch (nodeType) {
      case 'UserRegistrationNode':
        return {
          ...commonProperties,
          enableEmailVerification: true,
          passwordMinLength: 8,
          allowDuplicateEmail: false
        };
      case 'UserAuthenticationNode':
        return {
          ...commonProperties,
          tokenExpiry: 3600,
          maxAttempts: 3,
          lockoutDuration: 300
        };
      case 'DatabaseConnectionNode':
        return {
          ...commonProperties,
          maxConnections: 10,
          connectionTimeout: 30000,
          idleTimeout: 600000
        };
      case 'FileUploadNode':
        return {
          ...commonProperties,
          maxFileSize: 10485760,
          allowedTypes: ['*'],
          enableVirusScan: true
        };
      case 'JWTTokenNode':
        return {
          ...commonProperties,
          algorithm: 'HS256',
          expiresIn: '1h',
          issuer: 'dl-engine'
        };
      default:
        return commonProperties;
    }
  };

  // 获取面板样式
  const getPanelStyle = (): React.CSSProperties => {
    return {
      height: typeof height === 'number' ? `${height}px` : height,
      overflow: 'hidden'
    };
  };

  if (!visible) {
    return null;
  }

  return (
    <div style={getPanelStyle()}>
      <Spin spinning={loading} tip="处理中...">
        <Row gutter={[16, 16]} style={{ height: '100%' }}>
          {/* 服务器节点面板 */}
          <Col xs={24} sm={24} md={14} lg={16} xl={18}>
            <ServerSystemNodesPanel
              visible={visible}
              onNodeSelect={handleNodeSelect}
              onNodeAdd={handleNodeAdd}
              height="100%"
            />
          </Col>
          
          {/* 属性编辑面板 */}
          <Col xs={24} sm={24} md={10} lg={8} xl={6}>
            <ServerNodePropertyEditor
              node={selectedNode}
              onPropertyChange={handlePropertyChange}
              onSave={handlePropertySave}
              visible={!!selectedNode}
            />
          </Col>
        </Row>
      </Spin>
    </div>
  );
};

export default ServerSystemNodesPanelWrapper;
