/**
 * 边缘计算节点属性编辑器
 * 集成批次5：边缘计算扩展面板（59个节点）属性编辑功能实现
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Form,
  Input,
  InputNumber,
  Select,
  Switch,
  Slider,
  Space,
  Typography,
  Divider,
  Button,
  Tabs,
  Tag,
  Alert,
  Tooltip,
  message
} from 'antd';
import {
  SettingOutlined,
  InfoCircleOutlined,
  SaveOutlined,
  ReloadOutlined,
  CloudServerOutlined
} from '@ant-design/icons';

const { Text, Title } = Typography;
const { Option } = Select;
const { TextArea } = Input;
const { TabPane } = Tabs;

/**
 * 边缘计算节点属性接口
 */
export interface EdgeComputingNodeProperties {
  // 基础属性
  nodeId: string;
  nodeName: string;
  nodeType: string;
  category: string;
  description: string;
  
  // 网络配置
  endpoint?: string;
  port?: number;
  protocol?: string;
  timeout?: number;
  retryCount?: number;
  
  // 资源配置
  cpuLimit?: number;
  memoryLimit?: number;
  storageLimit?: number;
  bandwidth?: number;
  
  // 安全配置
  enableSecurity?: boolean;
  authMethod?: string;
  encryptionEnabled?: boolean;
  certificatePath?: string;
  
  // 监控配置
  enableMonitoring?: boolean;
  metricsInterval?: number;
  alertThreshold?: number;
  logLevel?: string;
  
  // 高级配置
  customConfig?: Record<string, any>;
  tags?: string[];
  metadata?: Record<string, any>;
}

/**
 * 属性编辑器组件属性
 */
export interface EdgeComputingNodePropertyEditorProps {
  nodeType: string;
  properties: EdgeComputingNodeProperties;
  onPropertiesChange?: (properties: EdgeComputingNodeProperties) => void;
  onSave?: (properties: EdgeComputingNodeProperties) => void;
  onReset?: () => void;
  readonly?: boolean;
  visible?: boolean;
}

/**
 * 边缘计算节点属性编辑器组件
 */
export const EdgeComputingNodePropertyEditor: React.FC<EdgeComputingNodePropertyEditorProps> = ({
  nodeType,
  properties,
  onPropertiesChange,
  onSave,
  onReset,
  readonly = false,
  visible = true
}) => {
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState('basic');
  const [hasChanges, setHasChanges] = useState(false);
  const [loading, setSaving] = useState(false);

  // 初始化表单
  useEffect(() => {
    form.setFieldsValue(properties);
    setHasChanges(false);
  }, [properties, form]);

  // 处理表单值变化
  const handleValuesChange = useCallback((changedValues: any, allValues: EdgeComputingNodeProperties) => {
    setHasChanges(true);
    onPropertiesChange?.(allValues);
  }, [onPropertiesChange]);

  // 保存配置
  const handleSave = useCallback(async () => {
    try {
      setSaving(true);
      const values = await form.validateFields();
      onSave?.(values);
      setHasChanges(false);
      message.success('节点属性保存成功');
    } catch (error) {
      message.error('保存失败，请检查输入');
    } finally {
      setSaving(false);
    }
  }, [form, onSave]);

  // 重置配置
  const handleReset = useCallback(() => {
    form.resetFields();
    setHasChanges(false);
    onReset?.();
    message.info('已重置为默认配置');
  }, [form, onReset]);

  // 获取节点类型显示名称
  const getNodeTypeDisplayName = (type: string): string => {
    const typeMap: Record<string, string> = {
      'EdgeRoutingNode': '边缘路由节点',
      'EdgeLoadBalancingNode': '边缘负载均衡节点',
      'EdgeCachingNode': '边缘缓存节点',
      'EdgeCompressionNode': '边缘压缩节点',
      'EdgeOptimizationNode': '边缘优化节点',
      'EdgeQoSNode': '边缘服务质量节点',
      'CloudEdgeOrchestrationNode': '云边协调节点',
      'HybridComputingNode': '混合计算节点',
      'DataSynchronizationNode': '数据同步节点',
      'TaskDistributionNode': '任务分发节点',
      'ResourceOptimizationNode': '资源优化节点',
      'LatencyOptimizationNode': '延迟优化节点',
      'BandwidthOptimizationNode': '带宽优化节点',
      'CostOptimizationNode': '成本优化节点',
      '5GConnectionNode': '5G连接节点',
      '5GSlicingNode': '5G网络切片节点',
      '5GMECNode': '5G移动边缘计算节点',
      '5GBeamformingNode': '5G波束成形节点',
      '5GMassiveMIMONode': '5G大规模MIMO节点',
      '5GURLLCNode': '5G超可靠低延迟节点',
      '5GeMBBNode': '5G增强移动宽带节点',
      '5GmMTCNode': '5G大规模机器通信节点',
      'EdgeDeviceRegistrationNode': '边缘设备注册节点',
      'EdgeDeviceMonitoringNode': '边缘设备监控节点',
      'EdgeDeviceControlNode': '边缘设备控制节点',
      'EdgeResourceManagementNode': '边缘资源管理节点',
      'EdgeNetworkNode': '边缘网络节点',
      'EdgeSecurityNode': '边缘安全节点',
      'EdgeUpdateNode': '边缘更新节点',
      'EdgeDiagnosticsNode': '边缘诊断节点',
      'EdgePerformanceNode': '边缘性能节点',
      'EdgeFailoverNode': '边缘故障转移节点',
      'EdgeConfigurationNode': '边缘配置节点',
      'EdgeMaintenanceNode': '边缘维护节点',
      'EdgeBackupNode': '边缘备份节点',
      'EdgeSyncNode': '边缘同步节点',
      'EdgeAnalyticsNode': '边缘分析节点',
      'EdgeAIInferenceNode': '边缘AI推理节点',
      'EdgeModelDeploymentNode': '边缘模型部署节点',
      'EdgeModelOptimizationNode': '边缘模型优化节点',
      'EdgeFederatedLearningNode': '边缘联邦学习节点',
      'EdgeAIMonitoringNode': '边缘AI监控节点',
      'EdgeAIPerformanceNode': '边缘AI性能节点',
      'EdgeAISecurityNode': '边缘AI安全节点',
      'EdgeAIAnalyticsNode': '边缘AI分析节点',
      'EdgeModelCacheNode': '边缘模型缓存节点'
    };
    return typeMap[type] || type;
  };

  if (!visible) {
    return null;
  }

  return (
    <Card
      title={
        <Space>
          <CloudServerOutlined />
          <Title level={5} style={{ margin: 0 }}>
            {getNodeTypeDisplayName(nodeType)} - 属性配置
          </Title>
          {hasChanges && <Tag color="orange">未保存</Tag>}
        </Space>
      }
      extra={
        !readonly && (
          <Space>
            <Button
              size="small"
              icon={<ReloadOutlined />}
              onClick={handleReset}
              disabled={!hasChanges}
            >
              重置
            </Button>
            <Button
              type="primary"
              size="small"
              icon={<SaveOutlined />}
              onClick={handleSave}
              loading={loading}
              disabled={!hasChanges}
            >
              保存
            </Button>
          </Space>
        )
      }
      size="small"
      style={{ height: '100%' }}
      bodyStyle={{ padding: '16px', height: 'calc(100% - 57px)', overflow: 'auto' }}
    >
      {hasChanges && (
        <Alert
          message="配置已修改"
          description="请保存配置以应用更改"
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      <Form
        form={form}
        layout="vertical"
        initialValues={properties}
        onValuesChange={handleValuesChange}
        disabled={readonly}
      >
        <Tabs activeKey={activeTab} onChange={setActiveTab} size="small">
          {/* 基础配置 */}
          <TabPane tab="基础配置" key="basic">
            <Form.Item
              label="节点ID"
              name="nodeId"
              rules={[{ required: true, message: '请输入节点ID' }]}
            >
              <Input placeholder="输入节点唯一标识" />
            </Form.Item>

            <Form.Item
              label="节点名称"
              name="nodeName"
              rules={[{ required: true, message: '请输入节点名称' }]}
            >
              <Input placeholder="输入节点显示名称" />
            </Form.Item>

            <Form.Item
              label="节点描述"
              name="description"
            >
              <TextArea 
                rows={3} 
                placeholder="输入节点功能描述"
                showCount
                maxLength={200}
              />
            </Form.Item>

            <Form.Item
              label="标签"
              name="tags"
            >
              <Select
                mode="tags"
                placeholder="添加标签"
                style={{ width: '100%' }}
              >
                <Option value="高性能">高性能</Option>
                <Option value="低延迟">低延迟</Option>
                <Option value="高可用">高可用</Option>
                <Option value="安全">安全</Option>
                <Option value="智能">智能</Option>
              </Select>
            </Form.Item>
          </TabPane>

          {/* 网络配置 */}
          <TabPane tab="网络配置" key="network">
            <Form.Item
              label="服务端点"
              name="endpoint"
            >
              <Input placeholder="http://localhost:8080" />
            </Form.Item>

            <Form.Item
              label="端口"
              name="port"
            >
              <InputNumber
                min={1}
                max={65535}
                placeholder="8080"
                style={{ width: '100%' }}
              />
            </Form.Item>

            <Form.Item
              label="协议"
              name="protocol"
            >
              <Select placeholder="选择协议">
                <Option value="http">HTTP</Option>
                <Option value="https">HTTPS</Option>
                <Option value="tcp">TCP</Option>
                <Option value="udp">UDP</Option>
                <Option value="websocket">WebSocket</Option>
              </Select>
            </Form.Item>

            <Form.Item
              label={
                <Space>
                  <span>超时时间 (秒)</span>
                  <Tooltip title="网络请求超时时间">
                    <InfoCircleOutlined />
                  </Tooltip>
                </Space>
              }
              name="timeout"
            >
              <Slider
                min={1}
                max={300}
                marks={{
                  1: '1s',
                  30: '30s',
                  60: '1m',
                  300: '5m'
                }}
              />
            </Form.Item>

            <Form.Item
              label="重试次数"
              name="retryCount"
            >
              <InputNumber
                min={0}
                max={10}
                style={{ width: '100%' }}
              />
            </Form.Item>
          </TabPane>

          {/* 资源配置 */}
          <TabPane tab="资源配置" key="resources">
            <Form.Item
              label="CPU限制 (%)"
              name="cpuLimit"
            >
              <Slider
                min={1}
                max={100}
                marks={{
                  1: '1%',
                  25: '25%',
                  50: '50%',
                  75: '75%',
                  100: '100%'
                }}
              />
            </Form.Item>

            <Form.Item
              label="内存限制 (GB)"
              name="memoryLimit"
            >
              <Slider
                min={0.5}
                max={32}
                step={0.5}
                marks={{
                  0.5: '0.5GB',
                  2: '2GB',
                  8: '8GB',
                  16: '16GB',
                  32: '32GB'
                }}
              />
            </Form.Item>

            <Form.Item
              label="存储限制 (GB)"
              name="storageLimit"
            >
              <InputNumber
                min={1}
                max={1000}
                style={{ width: '100%' }}
                placeholder="100"
              />
            </Form.Item>

            <Form.Item
              label="带宽限制 (Mbps)"
              name="bandwidth"
            >
              <InputNumber
                min={1}
                max={10000}
                style={{ width: '100%' }}
                placeholder="1000"
              />
            </Form.Item>
          </TabPane>

          {/* 安全配置 */}
          <TabPane tab="安全配置" key="security">
            <Form.Item
              label="启用安全"
              name="enableSecurity"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>

            <Form.Item
              label="认证方式"
              name="authMethod"
            >
              <Select placeholder="选择认证方式">
                <Option value="none">无认证</Option>
                <Option value="basic">基础认证</Option>
                <Option value="token">Token认证</Option>
                <Option value="oauth">OAuth认证</Option>
                <Option value="certificate">证书认证</Option>
              </Select>
            </Form.Item>

            <Form.Item
              label="启用加密"
              name="encryptionEnabled"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>

            <Form.Item
              label="证书路径"
              name="certificatePath"
            >
              <Input placeholder="/path/to/certificate.pem" />
            </Form.Item>
          </TabPane>

          {/* 监控配置 */}
          <TabPane tab="监控配置" key="monitoring">
            <Form.Item
              label="启用监控"
              name="enableMonitoring"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>

            <Form.Item
              label="指标收集间隔 (秒)"
              name="metricsInterval"
            >
              <Slider
                min={1}
                max={300}
                marks={{
                  1: '1s',
                  10: '10s',
                  30: '30s',
                  60: '1m',
                  300: '5m'
                }}
              />
            </Form.Item>

            <Form.Item
              label="告警阈值 (%)"
              name="alertThreshold"
            >
              <Slider
                min={50}
                max={100}
                marks={{
                  50: '50%',
                  70: '70%',
                  80: '80%',
                  90: '90%',
                  100: '100%'
                }}
              />
            </Form.Item>

            <Form.Item
              label="日志级别"
              name="logLevel"
            >
              <Select placeholder="选择日志级别">
                <Option value="debug">Debug</Option>
                <Option value="info">Info</Option>
                <Option value="warn">Warning</Option>
                <Option value="error">Error</Option>
              </Select>
            </Form.Item>
          </TabPane>
        </Tabs>
      </Form>
    </Card>
  );
};

export default EdgeComputingNodePropertyEditor;
