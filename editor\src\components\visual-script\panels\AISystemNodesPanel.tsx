/**
 * AI系统节点面板组件
 * 集成批次3：显示和管理82个AI系统节点，包括6个子分类
 */

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  Card,
  Collapse,
  List,
  Tag,
  Space,
  Button,
  Tooltip,
  Badge,
  Typography,
  Divider,
  Empty,
  Spin,
  Input,
  Select,
  Row,
  Col
} from 'antd';
import {
  RobotOutlined,
  BrainOutlined,
  ExperimentOutlined,
  CloudOutlined,
  MessageOutlined,
  SettingOutlined,
  InfoCircleOutlined,
  PlusOutlined,
  SearchOutlined,
  EyeOutlined,
  BuildOutlined,
  ThunderboltOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { DraggableAINode } from '../nodes/AINodeDragDrop';

const { Panel } = Collapse;
const { Text, Title } = Typography;
const { Search } = Input;
const { Option } = Select;

// AI系统节点分类枚举
export enum AINodeCategory {
  DEEP_LEARNING = 'deepLearning',
  MACHINE_LEARNING = 'machineLearning',
  AI_TOOLS = 'aiTools',
  AI_SERVICES = 'aiServices',
  NATURAL_LANGUAGE_PROCESSING = 'naturalLanguageProcessing',
  MODEL_MANAGEMENT = 'modelManagement'
}

// AI系统节点分类映射
export const AI_CATEGORY_MAP = {
  [AINodeCategory.DEEP_LEARNING]: {
    name: '深度学习',
    icon: <BrainOutlined />,
    color: '#ff6b6b',
    description: '深度学习模型和算法节点'
  },
  [AINodeCategory.MACHINE_LEARNING]: {
    name: '机器学习',
    icon: <ExperimentOutlined />,
    color: '#4ecdc4',
    description: '传统机器学习算法节点'
  },
  [AINodeCategory.AI_TOOLS]: {
    name: 'AI工具',
    icon: <BuildOutlined />,
    color: '#45b7d1',
    description: 'AI开发和部署工具节点'
  },
  [AINodeCategory.AI_SERVICES]: {
    name: 'AI服务',
    icon: <CloudOutlined />,
    color: '#f9ca24',
    description: 'AI云服务和API节点'
  },
  [AINodeCategory.NATURAL_LANGUAGE_PROCESSING]: {
    name: '自然语言处理',
    icon: <MessageOutlined />,
    color: '#6c5ce7',
    description: 'NLP和文本处理节点'
  },
  [AINodeCategory.MODEL_MANAGEMENT]: {
    name: '模型管理',
    icon: <SettingOutlined />,
    color: '#a29bfe',
    description: '模型生命周期管理节点'
  }
};

// 节点接口定义
export interface AINodeItem {
  id: string;
  name: string;
  type: string;
  category: AINodeCategory;
  description: string;
  icon?: React.ReactNode;
  tags?: string[];
  complexity?: 'basic' | 'intermediate' | 'advanced';
  inputs?: Array<{ name: string; type: string; description: string }>;
  outputs?: Array<{ name: string; type: string; description: string }>;
  properties?: Array<{ name: string; type: string; defaultValue: any; description: string }>;
}

// 组件属性接口
interface AISystemNodesPanelProps {
  visible?: boolean;
  onNodeSelect?: (node: AINodeItem) => void;
  onNodeAdd?: (nodeType: string) => void;
  height?: number | string;
  width?: number | string;
}

/**
 * AI系统节点面板组件
 */
const AISystemNodesPanel: React.FC<AISystemNodesPanelProps> = ({
  visible = true,
  onNodeSelect,
  onNodeAdd,
  height = '100%',
  width = '100%'
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [expandedPanels, setExpandedPanels] = useState<string[]>(['deepLearning']);
  const [filteredNodes, setFilteredNodes] = useState<AINodeItem[]>([]);

  // 深度学习节点（15个）
  const deepLearningNodes: AINodeItem[] = useMemo(() => [
    {
      id: 'neural-network',
      name: '神经网络',
      type: 'NeuralNetwork',
      category: AINodeCategory.DEEP_LEARNING,
      description: '创建和训练神经网络',
      complexity: 'intermediate',
      tags: ['神经网络', '训练']
    },
    {
      id: 'convolutional-layer',
      name: '卷积层',
      type: 'ConvolutionalLayer',
      category: AINodeCategory.DEEP_LEARNING,
      description: '添加卷积神经网络层',
      complexity: 'intermediate',
      tags: ['卷积', 'CNN']
    },
    {
      id: 'pooling-layer',
      name: '池化层',
      type: 'PoolingLayer',
      category: AINodeCategory.DEEP_LEARNING,
      description: '添加池化层',
      complexity: 'basic',
      tags: ['池化', '降维']
    },
    {
      id: 'fully-connected-layer',
      name: '全连接层',
      type: 'FullyConnectedLayer',
      category: AINodeCategory.DEEP_LEARNING,
      description: '添加全连接层',
      complexity: 'basic',
      tags: ['全连接', '密集']
    },
    {
      id: 'activation-function',
      name: '激活函数',
      type: 'ActivationFunction',
      category: AINodeCategory.DEEP_LEARNING,
      description: '设置激活函数',
      complexity: 'basic',
      tags: ['激活', '函数']
    },
    {
      id: 'loss-calculation',
      name: '损失计算',
      type: 'LossCalculation',
      category: AINodeCategory.DEEP_LEARNING,
      description: '计算训练损失',
      complexity: 'intermediate',
      tags: ['损失', '计算']
    },
    {
      id: 'optimizer',
      name: '优化器',
      type: 'Optimizer',
      category: AINodeCategory.DEEP_LEARNING,
      description: '设置网络优化器',
      complexity: 'intermediate',
      tags: ['优化器', 'SGD', 'Adam']
    },
    {
      id: 'backpropagation',
      name: '反向传播',
      type: 'Backpropagation',
      category: AINodeCategory.DEEP_LEARNING,
      description: '执行反向传播算法',
      complexity: 'advanced',
      tags: ['反向传播', '梯度']
    },
    {
      id: 'batch-normalization',
      name: '批归一化',
      type: 'BatchNormalization',
      category: AINodeCategory.DEEP_LEARNING,
      description: '添加批归一化层',
      complexity: 'intermediate',
      tags: ['归一化', '批处理']
    },
    {
      id: 'dropout-layer',
      name: 'Dropout层',
      type: 'DropoutLayer',
      category: AINodeCategory.DEEP_LEARNING,
      description: '添加Dropout正则化',
      complexity: 'basic',
      tags: ['Dropout', '正则化']
    },
    {
      id: 'transfer-learning',
      name: '迁移学习',
      type: 'TransferLearning',
      category: AINodeCategory.DEEP_LEARNING,
      description: '实现迁移学习',
      complexity: 'advanced',
      tags: ['迁移学习', '预训练']
    },
    {
      id: 'model-training',
      name: '模型训练',
      type: 'ModelTraining',
      category: AINodeCategory.DEEP_LEARNING,
      description: '训练深度学习模型',
      complexity: 'advanced',
      tags: ['训练', '模型']
    },
    {
      id: 'model-inference',
      name: '模型推理',
      type: 'ModelInference',
      category: AINodeCategory.DEEP_LEARNING,
      description: '执行模型推理',
      complexity: 'intermediate',
      tags: ['推理', '预测']
    },
    {
      id: 'model-evaluation',
      name: '模型评估',
      type: 'ModelEvaluation',
      category: AINodeCategory.DEEP_LEARNING,
      description: '评估模型性能',
      complexity: 'intermediate',
      tags: ['评估', '性能']
    },
    {
      id: 'hyperparameter-tuning',
      name: '超参数调优',
      type: 'HyperparameterTuning',
      category: AINodeCategory.DEEP_LEARNING,
      description: '自动调优超参数',
      complexity: 'advanced',
      tags: ['超参数', '调优']
    }
  ], []);

  // 机器学习节点（10个）
  const machineLearningNodes: AINodeItem[] = useMemo(() => [
    {
      id: 'linear-regression',
      name: '线性回归',
      type: 'LinearRegression',
      category: AINodeCategory.MACHINE_LEARNING,
      description: '执行线性回归分析',
      complexity: 'basic',
      tags: ['回归', '线性']
    },
    {
      id: 'logistic-regression',
      name: '逻辑回归',
      type: 'LogisticRegression',
      category: AINodeCategory.MACHINE_LEARNING,
      description: '执行逻辑回归分析',
      complexity: 'basic',
      tags: ['回归', '分类']
    },
    {
      id: 'decision-tree',
      name: '决策树',
      type: 'DecisionTree',
      category: AINodeCategory.MACHINE_LEARNING,
      description: '创建决策树模型',
      complexity: 'intermediate',
      tags: ['决策树', '分类']
    },
    {
      id: 'random-forest',
      name: '随机森林',
      type: 'RandomForest',
      category: AINodeCategory.MACHINE_LEARNING,
      description: '创建随机森林模型',
      complexity: 'intermediate',
      tags: ['随机森林', '集成']
    },
    {
      id: 'support-vector-machine',
      name: '支持向量机',
      type: 'SupportVectorMachine',
      category: AINodeCategory.MACHINE_LEARNING,
      description: '创建SVM模型',
      complexity: 'advanced',
      tags: ['SVM', '分类']
    },
    {
      id: 'kmeans-clustering',
      name: 'K均值聚类',
      type: 'KMeansClustering',
      category: AINodeCategory.MACHINE_LEARNING,
      description: '执行K均值聚类',
      complexity: 'intermediate',
      tags: ['聚类', 'K均值']
    },
    {
      id: 'pca',
      name: '主成分分析',
      type: 'PCA',
      category: AINodeCategory.MACHINE_LEARNING,
      description: '执行主成分分析',
      complexity: 'intermediate',
      tags: ['PCA', '降维']
    },
    {
      id: 'feature-selection',
      name: '特征选择',
      type: 'FeatureSelection',
      category: AINodeCategory.MACHINE_LEARNING,
      description: '选择重要特征',
      complexity: 'intermediate',
      tags: ['特征', '选择']
    },
    {
      id: 'data-preprocessing',
      name: '数据预处理',
      type: 'DataPreprocessing',
      category: AINodeCategory.MACHINE_LEARNING,
      description: '预处理训练数据',
      complexity: 'basic',
      tags: ['预处理', '数据']
    },
    {
      id: 'cross-validation',
      name: '交叉验证',
      type: 'CrossValidation',
      category: AINodeCategory.MACHINE_LEARNING,
      description: '执行交叉验证',
      complexity: 'intermediate',
      tags: ['验证', '交叉']
    }
  ], []);

  // AI工具节点（10个）
  const aiToolsNodes: AINodeItem[] = useMemo(() => [
    {
      id: 'model-deployment',
      name: '模型部署',
      type: 'ModelDeployment',
      category: AINodeCategory.AI_TOOLS,
      description: '部署AI模型',
      complexity: 'advanced',
      tags: ['部署', '模型']
    },
    {
      id: 'model-monitoring',
      name: '模型监控',
      type: 'ModelMonitoring',
      category: AINodeCategory.AI_TOOLS,
      description: '监控模型性能',
      complexity: 'intermediate',
      tags: ['监控', '性能']
    },
    {
      id: 'model-versioning',
      name: '模型版本管理',
      type: 'ModelVersioning',
      category: AINodeCategory.AI_TOOLS,
      description: '管理模型版本',
      complexity: 'intermediate',
      tags: ['版本', '管理']
    },
    {
      id: 'auto-ml',
      name: '自动机器学习',
      type: 'AutoML',
      category: AINodeCategory.AI_TOOLS,
      description: '自动化机器学习',
      complexity: 'advanced',
      tags: ['AutoML', '自动化']
    },
    {
      id: 'explainable-ai',
      name: '可解释AI',
      type: 'ExplainableAI',
      category: AINodeCategory.AI_TOOLS,
      description: '解释AI决策',
      complexity: 'advanced',
      tags: ['可解释', '决策']
    },
    {
      id: 'ai-ethics',
      name: 'AI伦理',
      type: 'AIEthics',
      category: AINodeCategory.AI_TOOLS,
      description: '检查AI伦理问题',
      complexity: 'advanced',
      tags: ['伦理', '检查']
    },
    {
      id: 'model-compression',
      name: '模型压缩',
      type: 'ModelCompression',
      category: AINodeCategory.AI_TOOLS,
      description: '压缩AI模型',
      complexity: 'advanced',
      tags: ['压缩', '优化']
    },
    {
      id: 'quantization',
      name: '量化',
      type: 'Quantization',
      category: AINodeCategory.AI_TOOLS,
      description: '量化模型参数',
      complexity: 'advanced',
      tags: ['量化', '参数']
    },
    {
      id: 'pruning',
      name: '剪枝',
      type: 'Pruning',
      category: AINodeCategory.AI_TOOLS,
      description: '剪枝模型结构',
      complexity: 'advanced',
      tags: ['剪枝', '结构']
    },
    {
      id: 'distillation',
      name: '知识蒸馏',
      type: 'Distillation',
      category: AINodeCategory.AI_TOOLS,
      description: '知识蒸馏技术',
      complexity: 'advanced',
      tags: ['蒸馏', '知识']
    }
  ], []);

  // AI服务节点（15个）
  const aiServicesNodes: AINodeItem[] = useMemo(() => [
    {
      id: 'ai-model-load',
      name: 'AI模型加载',
      type: 'AIModelLoad',
      category: AINodeCategory.AI_SERVICES,
      description: '加载预训练模型',
      complexity: 'basic',
      tags: ['加载', '模型']
    },
    {
      id: 'ai-inference',
      name: 'AI推理',
      type: 'AIInference',
      category: AINodeCategory.AI_SERVICES,
      description: '执行AI推理',
      complexity: 'intermediate',
      tags: ['推理', 'API']
    },
    {
      id: 'ai-training',
      name: 'AI训练',
      type: 'AITraining',
      category: AINodeCategory.AI_SERVICES,
      description: '训练AI模型',
      complexity: 'advanced',
      tags: ['训练', '云服务']
    },
    {
      id: 'ai-dataset',
      name: 'AI数据集',
      type: 'AIDataset',
      category: AINodeCategory.AI_SERVICES,
      description: '管理训练数据集',
      complexity: 'intermediate',
      tags: ['数据集', '管理']
    },
    {
      id: 'ai-metrics',
      name: 'AI指标',
      type: 'AIMetrics',
      category: AINodeCategory.AI_SERVICES,
      description: '计算AI性能指标',
      complexity: 'intermediate',
      tags: ['指标', '性能']
    },
    {
      id: 'ai-visualization',
      name: 'AI可视化',
      type: 'AIVisualization',
      category: AINodeCategory.AI_SERVICES,
      description: '可视化AI结果',
      complexity: 'intermediate',
      tags: ['可视化', '结果']
    },
    {
      id: 'ai-optimization',
      name: 'AI优化',
      type: 'AIOptimization',
      category: AINodeCategory.AI_SERVICES,
      description: '优化AI性能',
      complexity: 'advanced',
      tags: ['优化', '性能']
    },
    {
      id: 'ai-benchmark',
      name: 'AI基准测试',
      type: 'AIBenchmark',
      category: AINodeCategory.AI_SERVICES,
      description: '测试AI性能',
      complexity: 'intermediate',
      tags: ['基准', '测试']
    },
    {
      id: 'ai-validation',
      name: 'AI验证',
      type: 'AIValidation',
      category: AINodeCategory.AI_SERVICES,
      description: '验证AI模型',
      complexity: 'intermediate',
      tags: ['验证', '模型']
    },
    {
      id: 'ai-testing',
      name: 'AI测试',
      type: 'AITesting',
      category: AINodeCategory.AI_SERVICES,
      description: '测试AI功能',
      complexity: 'intermediate',
      tags: ['测试', '功能']
    },
    {
      id: 'ai-debugging',
      name: 'AI调试',
      type: 'AIDebugging',
      category: AINodeCategory.AI_SERVICES,
      description: '调试AI问题',
      complexity: 'advanced',
      tags: ['调试', '问题']
    },
    {
      id: 'ai-profiling',
      name: 'AI性能分析',
      type: 'AIProfiling',
      category: AINodeCategory.AI_SERVICES,
      description: '分析AI性能',
      complexity: 'advanced',
      tags: ['分析', '性能']
    },
    {
      id: 'ai-logging',
      name: 'AI日志',
      type: 'AILogging',
      category: AINodeCategory.AI_SERVICES,
      description: '记录AI运行日志',
      complexity: 'basic',
      tags: ['日志', '记录']
    },
    {
      id: 'ai-configuration',
      name: 'AI配置',
      type: 'AIConfiguration',
      category: AINodeCategory.AI_SERVICES,
      description: '配置AI参数',
      complexity: 'basic',
      tags: ['配置', '参数']
    },
    {
      id: 'ai-integration',
      name: 'AI集成',
      type: 'AIIntegration',
      category: AINodeCategory.AI_SERVICES,
      description: '集成AI服务',
      complexity: 'intermediate',
      tags: ['集成', '服务']
    }
  ], []);

  // 自然语言处理节点（7个）
  const nlpNodes: AINodeItem[] = useMemo(() => [
    {
      id: 'text-classification',
      name: '文本分类',
      type: 'TextClassification',
      category: AINodeCategory.NATURAL_LANGUAGE_PROCESSING,
      description: '对文本进行分类',
      complexity: 'intermediate',
      tags: ['文本', '分类']
    },
    {
      id: 'sentiment-analysis',
      name: '情感分析',
      type: 'SentimentAnalysis',
      category: AINodeCategory.NATURAL_LANGUAGE_PROCESSING,
      description: '分析文本情感',
      complexity: 'intermediate',
      tags: ['情感', '分析']
    },
    {
      id: 'named-entity-recognition',
      name: '命名实体识别',
      type: 'NamedEntityRecognition',
      category: AINodeCategory.NATURAL_LANGUAGE_PROCESSING,
      description: '识别文本实体',
      complexity: 'advanced',
      tags: ['实体', '识别']
    },
    {
      id: 'text-summarization',
      name: '文本摘要',
      type: 'TextSummarization',
      category: AINodeCategory.NATURAL_LANGUAGE_PROCESSING,
      description: '生成文本摘要',
      complexity: 'advanced',
      tags: ['摘要', '生成']
    },
    {
      id: 'machine-translation',
      name: '机器翻译',
      type: 'MachineTranslation',
      category: AINodeCategory.NATURAL_LANGUAGE_PROCESSING,
      description: '翻译文本',
      complexity: 'advanced',
      tags: ['翻译', '多语言']
    },
    {
      id: 'question-answering',
      name: '问答系统',
      type: 'QuestionAnswering',
      category: AINodeCategory.NATURAL_LANGUAGE_PROCESSING,
      description: '回答问题',
      complexity: 'advanced',
      tags: ['问答', '系统']
    },
    {
      id: 'text-generation',
      name: '文本生成',
      type: 'TextGeneration',
      category: AINodeCategory.NATURAL_LANGUAGE_PROCESSING,
      description: '生成新文本',
      complexity: 'advanced',
      tags: ['生成', '文本']
    }
  ], []);

  // 模型管理节点（25个）
  const modelManagementNodes: AINodeItem[] = useMemo(() => [
    {
      id: 'model-registry',
      name: '模型注册表',
      type: 'ModelRegistry',
      category: AINodeCategory.MODEL_MANAGEMENT,
      description: '模型注册表节点',
      complexity: 'intermediate',
      tags: ['注册表', '模型']
    },
    {
      id: 'model-validation',
      name: '模型验证',
      type: 'ModelValidation',
      category: AINodeCategory.MODEL_MANAGEMENT,
      description: '模型验证节点',
      complexity: 'intermediate',
      tags: ['验证', '模型']
    },
    {
      id: 'model-testing',
      name: '模型测试',
      type: 'ModelTesting',
      category: AINodeCategory.MODEL_MANAGEMENT,
      description: '模型测试节点',
      complexity: 'intermediate',
      tags: ['测试', '模型']
    },
    {
      id: 'model-benchmark',
      name: '模型基准测试',
      type: 'ModelBenchmark',
      category: AINodeCategory.MODEL_MANAGEMENT,
      description: '模型基准测试节点',
      complexity: 'advanced',
      tags: ['基准', '测试']
    },
    {
      id: 'model-comparison',
      name: '模型比较',
      type: 'ModelComparison',
      category: AINodeCategory.MODEL_MANAGEMENT,
      description: '模型比较节点',
      complexity: 'intermediate',
      tags: ['比较', '模型']
    },
    {
      id: 'model-metrics',
      name: '模型指标',
      type: 'ModelMetrics',
      category: AINodeCategory.MODEL_MANAGEMENT,
      description: '模型指标节点',
      complexity: 'basic',
      tags: ['指标', '模型']
    },
    {
      id: 'model-audit',
      name: '模型审计',
      type: 'ModelAudit',
      category: AINodeCategory.MODEL_MANAGEMENT,
      description: '模型审计节点',
      complexity: 'advanced',
      tags: ['审计', '模型']
    },
    {
      id: 'model-governance',
      name: '模型治理',
      type: 'ModelGovernance',
      category: AINodeCategory.MODEL_MANAGEMENT,
      description: '模型治理节点',
      complexity: 'advanced',
      tags: ['治理', '模型']
    },
    {
      id: 'model-lifecycle',
      name: '模型生命周期',
      type: 'ModelLifecycle',
      category: AINodeCategory.MODEL_MANAGEMENT,
      description: '模型生命周期节点',
      complexity: 'advanced',
      tags: ['生命周期', '模型']
    },
    {
      id: 'model-rollback',
      name: '模型回滚',
      type: 'ModelRollback',
      category: AINodeCategory.MODEL_MANAGEMENT,
      description: '模型回滚节点',
      complexity: 'intermediate',
      tags: ['回滚', '模型']
    },
    {
      id: 'model-ab-test',
      name: '模型A/B测试',
      type: 'ModelABTest',
      category: AINodeCategory.MODEL_MANAGEMENT,
      description: '模型A/B测试节点',
      complexity: 'advanced',
      tags: ['A/B测试', '模型']
    },
    {
      id: 'model-canary',
      name: '模型金丝雀发布',
      type: 'ModelCanary',
      category: AINodeCategory.MODEL_MANAGEMENT,
      description: '模型金丝雀发布节点',
      complexity: 'advanced',
      tags: ['金丝雀', '发布']
    },
    {
      id: 'model-shadow',
      name: '模型影子测试',
      type: 'ModelShadow',
      category: AINodeCategory.MODEL_MANAGEMENT,
      description: '模型影子测试节点',
      complexity: 'advanced',
      tags: ['影子', '测试']
    },
    {
      id: 'model-feedback',
      name: '模型反馈',
      type: 'ModelFeedback',
      category: AINodeCategory.MODEL_MANAGEMENT,
      description: '模型反馈节点',
      complexity: 'intermediate',
      tags: ['反馈', '模型']
    },
    {
      id: 'model-retraining',
      name: '模型重训练',
      type: 'ModelRetraining',
      category: AINodeCategory.MODEL_MANAGEMENT,
      description: '模型重训练节点',
      complexity: 'advanced',
      tags: ['重训练', '模型']
    },
    {
      id: 'model-drift-detection',
      name: '模型漂移检测',
      type: 'ModelDriftDetection',
      category: AINodeCategory.MODEL_MANAGEMENT,
      description: '模型漂移检测节点',
      complexity: 'advanced',
      tags: ['漂移', '检测']
    },
    {
      id: 'model-performance',
      name: '模型性能监控',
      type: 'ModelPerformance',
      category: AINodeCategory.MODEL_MANAGEMENT,
      description: '模型性能监控节点',
      complexity: 'intermediate',
      tags: ['性能', '监控']
    },
    {
      id: 'model-resource',
      name: '模型资源管理',
      type: 'ModelResource',
      category: AINodeCategory.MODEL_MANAGEMENT,
      description: '模型资源管理节点',
      complexity: 'intermediate',
      tags: ['资源', '管理']
    },
    {
      id: 'model-security',
      name: '模型安全',
      type: 'ModelSecurity',
      category: AINodeCategory.MODEL_MANAGEMENT,
      description: '模型安全节点',
      complexity: 'advanced',
      tags: ['安全', '模型']
    },
    {
      id: 'model-privacy',
      name: '模型隐私保护',
      type: 'ModelPrivacy',
      category: AINodeCategory.MODEL_MANAGEMENT,
      description: '模型隐私保护节点',
      complexity: 'advanced',
      tags: ['隐私', '保护']
    },
    {
      id: 'model-fairness',
      name: '模型公平性',
      type: 'ModelFairness',
      category: AINodeCategory.MODEL_MANAGEMENT,
      description: '模型公平性节点',
      complexity: 'advanced',
      tags: ['公平性', '模型']
    },
    {
      id: 'model-interpretability',
      name: '模型可解释性',
      type: 'ModelInterpretability',
      category: AINodeCategory.MODEL_MANAGEMENT,
      description: '模型可解释性节点',
      complexity: 'advanced',
      tags: ['可解释性', '模型']
    },
    {
      id: 'model-documentation',
      name: '模型文档',
      type: 'ModelDocumentation',
      category: AINodeCategory.MODEL_MANAGEMENT,
      description: '模型文档节点',
      complexity: 'basic',
      tags: ['文档', '模型']
    },
    {
      id: 'model-collaboration',
      name: '模型协作',
      type: 'ModelCollaboration',
      category: AINodeCategory.MODEL_MANAGEMENT,
      description: '模型协作节点',
      complexity: 'intermediate',
      tags: ['协作', '模型']
    },
    {
      id: 'model-marketplace',
      name: '模型市场',
      type: 'ModelMarketplace',
      category: AINodeCategory.MODEL_MANAGEMENT,
      description: '模型市场节点',
      complexity: 'intermediate',
      tags: ['市场', '模型']
    }
  ], []);

  return (
    <div style={{ height, width, display: visible ? 'block' : 'none' }}>
      <Card
        title={
          <Space>
            <RobotOutlined style={{ color: '#1890ff' }} />
            <Text strong>AI系统节点面板</Text>
            <Badge count={82} style={{ backgroundColor: '#52c41a' }} />
          </Space>
        }
        size="small"
        style={{ height: '100%' }}
        bodyStyle={{ padding: '8px', height: 'calc(100% - 57px)', overflow: 'auto' }}
      >
        {/* 搜索和筛选区域 */}
        <Space direction="vertical" style={{ width: '100%', marginBottom: 16 }}>
          <Search
            placeholder="搜索AI节点..."
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ width: '100%' }}
            prefix={<SearchOutlined />}
          />
          <Select
            value={selectedCategory}
            onChange={setSelectedCategory}
            style={{ width: '100%' }}
            placeholder="选择分类"
          >
            <Option value="all">全部分类</Option>
            {Object.entries(AI_CATEGORY_MAP).map(([key, value]) => (
              <Option key={key} value={key}>
                {value.icon} {value.name}
              </Option>
            ))}
          </Select>
        </Space>

        {/* 节点分类面板 */}
        <Collapse
          activeKey={expandedPanels}
          onChange={setExpandedPanels}
          size="small"
        >
          {/* 深度学习面板 */}
          <Panel
            header={
              <Space>
                {AI_CATEGORY_MAP[AINodeCategory.DEEP_LEARNING].icon}
                <Text strong>{AI_CATEGORY_MAP[AINodeCategory.DEEP_LEARNING].name}</Text>
                <Badge count={15} style={{ backgroundColor: AI_CATEGORY_MAP[AINodeCategory.DEEP_LEARNING].color }} />
              </Space>
            }
            key={AINodeCategory.DEEP_LEARNING}
          >
            <List
              size="small"
              dataSource={deepLearningNodes}
              renderItem={(node) => (
                <DraggableAINode
                  key={node.id}
                  node={node}
                  onSelect={onNodeSelect}
                  onAdd={onNodeAdd}
                />
              )}
            />
          </Panel>

          {/* 机器学习面板 */}
          <Panel
            header={
              <Space>
                {AI_CATEGORY_MAP[AINodeCategory.MACHINE_LEARNING].icon}
                <Text strong>{AI_CATEGORY_MAP[AINodeCategory.MACHINE_LEARNING].name}</Text>
                <Badge count={10} style={{ backgroundColor: AI_CATEGORY_MAP[AINodeCategory.MACHINE_LEARNING].color }} />
              </Space>
            }
            key={AINodeCategory.MACHINE_LEARNING}
          >
            <List
              size="small"
              dataSource={machineLearningNodes}
              renderItem={(node) => (
                <DraggableAINode
                  key={node.id}
                  node={node}
                  onSelect={onNodeSelect}
                  onAdd={onNodeAdd}
                />
              )}
            />
          </Panel>

          {/* AI工具面板 */}
          <Panel
            header={
              <Space>
                {AI_CATEGORY_MAP[AINodeCategory.AI_TOOLS].icon}
                <Text strong>{AI_CATEGORY_MAP[AINodeCategory.AI_TOOLS].name}</Text>
                <Badge count={10} style={{ backgroundColor: AI_CATEGORY_MAP[AINodeCategory.AI_TOOLS].color }} />
              </Space>
            }
            key={AINodeCategory.AI_TOOLS}
          >
            <List
              size="small"
              dataSource={aiToolsNodes}
              renderItem={(node) => (
                <DraggableAINode
                  key={node.id}
                  node={node}
                  onSelect={onNodeSelect}
                  onAdd={onNodeAdd}
                />
              )}
            />
          </Panel>

          {/* AI服务面板 */}
          <Panel
            header={
              <Space>
                {AI_CATEGORY_MAP[AINodeCategory.AI_SERVICES].icon}
                <Text strong>{AI_CATEGORY_MAP[AINodeCategory.AI_SERVICES].name}</Text>
                <Badge count={15} style={{ backgroundColor: AI_CATEGORY_MAP[AINodeCategory.AI_SERVICES].color }} />
              </Space>
            }
            key={AINodeCategory.AI_SERVICES}
          >
            <List
              size="small"
              dataSource={aiServicesNodes}
              renderItem={(node) => (
                <DraggableAINode
                  key={node.id}
                  node={node}
                  onSelect={onNodeSelect}
                  onAdd={onNodeAdd}
                />
              )}
            />
          </Panel>

          {/* 自然语言处理面板 */}
          <Panel
            header={
              <Space>
                {AI_CATEGORY_MAP[AINodeCategory.NATURAL_LANGUAGE_PROCESSING].icon}
                <Text strong>{AI_CATEGORY_MAP[AINodeCategory.NATURAL_LANGUAGE_PROCESSING].name}</Text>
                <Badge count={7} style={{ backgroundColor: AI_CATEGORY_MAP[AINodeCategory.NATURAL_LANGUAGE_PROCESSING].color }} />
              </Space>
            }
            key={AINodeCategory.NATURAL_LANGUAGE_PROCESSING}
          >
            <List
              size="small"
              dataSource={nlpNodes}
              renderItem={(node) => (
                <DraggableAINode
                  key={node.id}
                  node={node}
                  onSelect={onNodeSelect}
                  onAdd={onNodeAdd}
                />
              )}
            />
          </Panel>

          {/* 模型管理面板 */}
          <Panel
            header={
              <Space>
                {AI_CATEGORY_MAP[AINodeCategory.MODEL_MANAGEMENT].icon}
                <Text strong>{AI_CATEGORY_MAP[AINodeCategory.MODEL_MANAGEMENT].name}</Text>
                <Badge count={25} style={{ backgroundColor: AI_CATEGORY_MAP[AINodeCategory.MODEL_MANAGEMENT].color }} />
              </Space>
            }
            key={AINodeCategory.MODEL_MANAGEMENT}
          >
            <List
              size="small"
              dataSource={modelManagementNodes}
              renderItem={(node) => (
                <DraggableAINode
                  key={node.id}
                  node={node}
                  onSelect={onNodeSelect}
                  onAdd={onNodeAdd}
                />
              )}
            />
          </Panel>
        </Collapse>
      </Card>
    </div>
  );
};

export default AISystemNodesPanel;
