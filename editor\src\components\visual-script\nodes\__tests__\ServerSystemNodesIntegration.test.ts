/**
 * 服务器系统节点集成测试
 * 测试集成批次4：58个服务器系统节点的集成功能
 */

import { ServerSystemNodesIntegration } from '../ServerSystemNodesIntegration';
import { ServerNodeCategory } from '../panels/ServerSystemNodesPanel';

// 模拟节点编辑器
class MockNodeEditor {
  private registeredNodes: Map<string, any> = new Map();
  private nodePalettes: Array<{ name: string; nodes: string[] }> = [];

  registerNode(nodeType: string, nodeConfig: any): void {
    this.registeredNodes.set(nodeType, nodeConfig);
  }

  addNodePalette(name: string, nodes: string[]): void {
    this.nodePalettes.push({ name, nodes });
  }

  getRegisteredNodes(): Map<string, any> {
    return this.registeredNodes;
  }

  getNodePalettes(): Array<{ name: string; nodes: string[] }> {
    return this.nodePalettes;
  }
}

describe('ServerSystemNodesIntegration', () => {
  let mockNodeEditor: MockNodeEditor;
  let integration: ServerSystemNodesIntegration;

  beforeEach(() => {
    mockNodeEditor = new MockNodeEditor();
    integration = new ServerSystemNodesIntegration(mockNodeEditor as any);
  });

  describe('节点注册', () => {
    test('应该注册所有58个服务器系统节点', () => {
      const registeredNodes = integration.getRegisteredNodes();
      expect(registeredNodes.size).toBeGreaterThanOrEqual(58);
    });

    test('应该包含所有7个节点分类', () => {
      const categories = integration.getNodeCategories();
      expect(categories.size).toBe(7);
      
      expect(categories.has('用户服务')).toBe(true);
      expect(categories.has('数据服务')).toBe(true);
      expect(categories.has('文件服务')).toBe(true);
      expect(categories.has('认证授权')).toBe(true);
      expect(categories.has('通知服务')).toBe(true);
      expect(categories.has('监控服务')).toBe(true);
      expect(categories.has('项目管理')).toBe(true);
    });

    test('应该正确注册用户服务节点', () => {
      const registeredNodes = integration.getRegisteredNodes();
      
      expect(registeredNodes.has('UserRegistrationNode')).toBe(true);
      expect(registeredNodes.has('UserAuthenticationNode')).toBe(true);
      expect(registeredNodes.has('UserProfileNode')).toBe(true);
      expect(registeredNodes.has('UserPermissionNode')).toBe(true);
      
      const userRegNode = registeredNodes.get('UserRegistrationNode');
      expect(userRegNode?.category).toBe(ServerNodeCategory.USER_SERVICE);
      expect(userRegNode?.name).toBe('用户注册');
    });

    test('应该正确注册数据服务节点', () => {
      const registeredNodes = integration.getRegisteredNodes();
      
      expect(registeredNodes.has('DatabaseConnectionNode')).toBe(true);
      expect(registeredNodes.has('DatabaseQueryNode')).toBe(true);
      
      const dbConnNode = registeredNodes.get('DatabaseConnectionNode');
      expect(dbConnNode?.category).toBe(ServerNodeCategory.DATA_SERVICE);
      expect(dbConnNode?.name).toBe('数据库连接');
    });

    test('应该正确注册文件服务节点', () => {
      const registeredNodes = integration.getRegisteredNodes();
      
      expect(registeredNodes.has('FileUploadNode')).toBe(true);
      expect(registeredNodes.has('FileDownloadNode')).toBe(true);
      
      const fileUploadNode = registeredNodes.get('FileUploadNode');
      expect(fileUploadNode?.category).toBe(ServerNodeCategory.FILE_SERVICE);
      expect(fileUploadNode?.name).toBe('文件上传');
    });

    test('应该正确注册认证授权节点', () => {
      const registeredNodes = integration.getRegisteredNodes();
      
      expect(registeredNodes.has('JWTTokenNode')).toBe(true);
      expect(registeredNodes.has('OAuth2Node')).toBe(true);
      
      const jwtNode = registeredNodes.get('JWTTokenNode');
      expect(jwtNode?.category).toBe(ServerNodeCategory.AUTH_SERVICE);
      expect(jwtNode?.name).toBe('JWT令牌');
    });
  });

  describe('节点配置', () => {
    test('每个节点应该有正确的基本属性', () => {
      const registeredNodes = integration.getRegisteredNodes();
      
      registeredNodes.forEach((node, nodeType) => {
        expect(node.id).toBeDefined();
        expect(node.type).toBe(nodeType);
        expect(node.name).toBeDefined();
        expect(node.description).toBeDefined();
        expect(node.category).toBeDefined();
        expect(node.icon).toBeDefined();
        expect(node.color).toBeDefined();
        expect(node.complexity).toMatch(/^(basic|intermediate|advanced)$/);
        expect(Array.isArray(node.tags)).toBe(true);
        expect(Array.isArray(node.inputs)).toBe(true);
        expect(Array.isArray(node.outputs)).toBe(true);
        expect(Array.isArray(node.properties)).toBe(true);
        expect(node.version).toBe('1.0.0');
        expect(node.author).toBe('DL Engine Team');
      });
    });

    test('节点应该有正确的输入输出配置', () => {
      const registeredNodes = integration.getRegisteredNodes();
      const userRegNode = registeredNodes.get('UserRegistrationNode');
      
      expect(userRegNode?.inputs.length).toBeGreaterThan(0);
      expect(userRegNode?.outputs.length).toBeGreaterThan(0);
      
      // 检查输入配置
      userRegNode?.inputs.forEach((input: any) => {
        expect(input.name).toBeDefined();
        expect(input.type).toBeDefined();
        expect(input.description).toBeDefined();
      });
      
      // 检查输出配置
      userRegNode?.outputs.forEach((output: any) => {
        expect(output.name).toBeDefined();
        expect(output.type).toBeDefined();
        expect(output.description).toBeDefined();
      });
    });

    test('节点应该有正确的属性配置', () => {
      const registeredNodes = integration.getRegisteredNodes();
      const dbConnNode = registeredNodes.get('DatabaseConnectionNode');
      
      expect(dbConnNode?.properties.length).toBeGreaterThan(0);
      
      dbConnNode?.properties.forEach((property: any) => {
        expect(property.name).toBeDefined();
        expect(property.type).toBeDefined();
        expect(property.defaultValue).toBeDefined();
        expect(property.description).toBeDefined();
      });
    });
  });

  describe('节点编辑器集成', () => {
    test('应该将节点注册到节点编辑器', () => {
      const editorNodes = mockNodeEditor.getRegisteredNodes();
      expect(editorNodes.size).toBeGreaterThanOrEqual(58);
    });

    test('应该添加节点面板到编辑器', () => {
      const palettes = mockNodeEditor.getNodePalettes();
      expect(palettes.length).toBeGreaterThan(0);
      
      const serverPalette = palettes.find(p => p.name === '服务器系统节点');
      expect(serverPalette).toBeDefined();
      expect(serverPalette?.nodes.length).toBeGreaterThanOrEqual(58);
    });
  });

  describe('集成统计', () => {
    test('应该提供正确的集成统计信息', () => {
      const stats = integration.getIntegrationStats();
      
      expect(stats.totalNodes).toBeGreaterThanOrEqual(58);
      expect(stats.categories).toBe(7);
      expect(stats.userServiceNodes).toBe(12);
      expect(stats.dataServiceNodes).toBe(12);
      expect(stats.fileServiceNodes).toBe(10);
      expect(stats.authServiceNodes).toBe(7);
      expect(stats.notificationServiceNodes).toBe(8);
      expect(stats.monitoringServiceNodes).toBe(5);
      expect(stats.projectManagementNodes).toBe(4);
    });
  });

  describe('错误处理', () => {
    test('应该处理空的节点编辑器', () => {
      expect(() => {
        new ServerSystemNodesIntegration(null as any);
      }).not.toThrow();
    });

    test('应该处理节点编辑器方法缺失', () => {
      const incompleteEditor = {};
      expect(() => {
        new ServerSystemNodesIntegration(incompleteEditor as any);
      }).not.toThrow();
    });
  });
});

describe('服务器系统节点分类', () => {
  test('ServerNodeCategory 枚举应该包含所有分类', () => {
    expect(ServerNodeCategory.USER_SERVICE).toBe('userService');
    expect(ServerNodeCategory.DATA_SERVICE).toBe('dataService');
    expect(ServerNodeCategory.FILE_SERVICE).toBe('fileService');
    expect(ServerNodeCategory.AUTH_SERVICE).toBe('authService');
    expect(ServerNodeCategory.NOTIFICATION_SERVICE).toBe('notificationService');
    expect(ServerNodeCategory.MONITORING_SERVICE).toBe('monitoringService');
    expect(ServerNodeCategory.PROJECT_MANAGEMENT).toBe('projectManagement');
  });
});

describe('节点复杂度分类', () => {
  let integration: ServerSystemNodesIntegration;

  beforeEach(() => {
    const mockEditor = new MockNodeEditor();
    integration = new ServerSystemNodesIntegration(mockEditor as any);
  });

  test('应该正确分类基础节点', () => {
    const registeredNodes = integration.getRegisteredNodes();
    const basicNodes = Array.from(registeredNodes.values()).filter(
      node => node.complexity === 'basic'
    );
    
    expect(basicNodes.length).toBeGreaterThan(0);
  });

  test('应该正确分类中级节点', () => {
    const registeredNodes = integration.getRegisteredNodes();
    const intermediateNodes = Array.from(registeredNodes.values()).filter(
      node => node.complexity === 'intermediate'
    );
    
    expect(intermediateNodes.length).toBeGreaterThan(0);
  });

  test('应该正确分类高级节点', () => {
    const registeredNodes = integration.getRegisteredNodes();
    const advancedNodes = Array.from(registeredNodes.values()).filter(
      node => node.complexity === 'advanced'
    );
    
    expect(advancedNodes.length).toBeGreaterThan(0);
  });
});
