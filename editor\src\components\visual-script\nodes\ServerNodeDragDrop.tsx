/**
 * 服务器节点拖拽组件
 * 支持服务器系统节点的拖拽功能
 */

import React, { useState, useCallback } from 'react';
import { useDrag, useDrop } from 'react-dnd';
import { List, Tag, Space, Button, Tooltip, Typography } from 'antd';
import {
  PlusOutlined,
  InfoCircleOutlined,
  EyeOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { ServerNodeItem } from '../panels/ServerSystemNodesPanel';

const { Text } = Typography;

// 拖拽项类型
export const ItemTypes = {
  SERVER_NODE: 'server_node'
};

// 拖拽项接口
interface DragItem {
  type: string;
  node: ServerNodeItem;
}

// 拖拽节点组件属性
interface DraggableServerNodeProps {
  node: ServerNodeItem;
  onNodeSelect?: (node: ServerNodeItem) => void;
  onNodeAdd?: (nodeType: string) => void;
  onDragStart?: (node: ServerNodeItem) => void;
  onDragEnd?: (node: ServerNodeItem, didDrop: boolean) => void;
}

// 拖拽目标区域属性
interface ServerNodeDropZoneProps {
  onNodeDrop?: (node: ServerNodeItem, position: { x: number; y: number }) => void;
  children?: React.ReactNode;
  style?: React.CSSProperties;
}

/**
 * 可拖拽的服务器节点组件
 */
export const DraggableServerNode: React.FC<DraggableServerNodeProps> = ({
  node,
  onNodeSelect,
  onNodeAdd,
  onDragStart,
  onDragEnd
}) => {
  const [isHovered, setIsHovered] = useState(false);

  // 配置拖拽
  const [{ isDragging }, drag, preview] = useDrag({
    type: ItemTypes.SERVER_NODE,
    item: (): DragItem => {
      onDragStart?.(node);
      return { type: ItemTypes.SERVER_NODE, node };
    },
    end: (item, monitor) => {
      const didDrop = monitor.didDrop();
      onDragEnd?.(node, didDrop);
    },
    collect: (monitor) => ({
      isDragging: monitor.isDragging()
    })
  });

  // 处理节点点击
  const handleNodeClick = useCallback(() => {
    onNodeSelect?.(node);
  }, [node, onNodeSelect]);

  // 处理添加节点
  const handleAddNode = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    onNodeAdd?.(node.type);
  }, [node.type, onNodeAdd]);

  // 获取复杂度颜色
  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'basic':
        return '#52c41a';
      case 'intermediate':
        return '#fa8c16';
      case 'advanced':
        return '#f5222d';
      default:
        return '#d9d9d9';
    }
  };

  // 获取复杂度文本
  const getComplexityText = (complexity: string) => {
    switch (complexity) {
      case 'basic':
        return '基础';
      case 'intermediate':
        return '中级';
      case 'advanced':
        return '高级';
      default:
        return '未知';
    }
  };

  return (
    <div ref={preview} style={{ opacity: isDragging ? 0.5 : 1 }}>
      <List.Item
        ref={drag}
        style={{
          cursor: 'grab',
          padding: '8px 12px',
          margin: '4px 0',
          backgroundColor: isHovered ? '#f0f0f0' : 'transparent',
          borderRadius: '4px',
          border: '1px solid transparent',
          transition: 'all 0.2s ease'
        }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onClick={handleNodeClick}
        actions={[
          <Tooltip title="查看详情" key="view">
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                onNodeSelect?.(node);
              }}
            />
          </Tooltip>,
          <Tooltip title="添加到画布" key="add">
            <Button
              type="text"
              size="small"
              icon={<PlusOutlined />}
              onClick={handleAddNode}
            />
          </Tooltip>
        ]}
      >
        <List.Item.Meta
          avatar={
            <div
              style={{
                width: 32,
                height: 32,
                borderRadius: '50%',
                backgroundColor: node.color,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '16px',
                color: 'white'
              }}
            >
              {node.icon}
            </div>
          }
          title={
            <Space>
              <Text strong style={{ fontSize: '13px' }}>
                {node.name}
              </Text>
              <Tag
                color={getComplexityColor(node.complexity)}
                style={{ fontSize: '10px', padding: '0 4px', lineHeight: '16px' }}
              >
                {getComplexityText(node.complexity)}
              </Tag>
            </Space>
          }
          description={
            <div>
              <Text
                type="secondary"
                style={{
                  fontSize: '11px',
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical',
                  overflow: 'hidden',
                  lineHeight: '14px',
                  marginBottom: '4px'
                }}
              >
                {node.description}
              </Text>
              <div style={{ marginTop: '4px' }}>
                {node.tags.slice(0, 3).map((tag, index) => (
                  <Tag
                    key={index}
                    size="small"
                    style={{
                      fontSize: '10px',
                      padding: '0 4px',
                      lineHeight: '14px',
                      marginRight: '2px',
                      marginBottom: '2px'
                    }}
                  >
                    {tag}
                  </Tag>
                ))}
                {node.tags.length > 3 && (
                  <Tag
                    size="small"
                    style={{
                      fontSize: '10px',
                      padding: '0 4px',
                      lineHeight: '14px'
                    }}
                  >
                    +{node.tags.length - 3}
                  </Tag>
                )}
              </div>
            </div>
          }
        />
      </List.Item>
    </div>
  );
};

/**
 * 服务器节点拖拽目标区域
 */
export const ServerNodeDropZone: React.FC<ServerNodeDropZoneProps> = ({
  onNodeDrop,
  children,
  style
}) => {
  const [{ isOver, canDrop }, drop] = useDrop({
    accept: ItemTypes.SERVER_NODE,
    drop: (item: DragItem, monitor) => {
      const clientOffset = monitor.getClientOffset();
      if (clientOffset && onNodeDrop) {
        const position = {
          x: clientOffset.x,
          y: clientOffset.y
        };
        onNodeDrop(item.node, position);
      }
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop()
    })
  });

  const dropZoneStyle: React.CSSProperties = {
    ...style,
    backgroundColor: isOver && canDrop ? '#e6f7ff' : 'transparent',
    border: isOver && canDrop ? '2px dashed #1890ff' : 'none',
    transition: 'all 0.2s ease'
  };

  return (
    <div ref={drop} style={dropZoneStyle}>
      {children}
    </div>
  );
};

export default DraggableServerNode;
