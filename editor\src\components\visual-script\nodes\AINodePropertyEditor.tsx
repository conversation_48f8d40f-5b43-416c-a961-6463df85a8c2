/**
 * AI节点属性编辑器
 * 为AI系统节点提供属性编辑功能
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  InputNumber,
  Select,
  Switch,
  Slider,
  Button,
  Space,
  Divider,
  Typography,
  Collapse,
  Tag,
  Tooltip,
  Alert
} from 'antd';
import {
  SettingOutlined,
  InfoCircleOutlined,
  SaveOutlined,
  ReloadOutlined,
  BrainOutlined,
  ExperimentOutlined,
  BuildOutlined,
  CloudOutlined,
  MessageOutlined
} from '@ant-design/icons';
import { AINodeItem, AINodeCategory } from '../panels/AISystemNodesPanel';

const { Text, Title } = Typography;
const { Panel } = Collapse;
const { Option } = Select;
const { TextArea } = Input;

// 属性编辑器组件属性
interface AINodePropertyEditorProps {
  node?: AINodeItem;
  onPropertyChange?: (nodeId: string, property: string, value: any) => void;
  onSave?: (nodeId: string, properties: Record<string, any>) => void;
  visible?: boolean;
}

// 分类图标映射
const CATEGORY_ICONS = {
  [AINodeCategory.DEEP_LEARNING]: <BrainOutlined />,
  [AINodeCategory.MACHINE_LEARNING]: <ExperimentOutlined />,
  [AINodeCategory.AI_TOOLS]: <BuildOutlined />,
  [AINodeCategory.AI_SERVICES]: <CloudOutlined />,
  [AINodeCategory.NATURAL_LANGUAGE_PROCESSING]: <MessageOutlined />,
  [AINodeCategory.MODEL_MANAGEMENT]: <SettingOutlined />
};

// 默认属性配置
const getDefaultProperties = (nodeType: string): Record<string, any> => {
  const commonProperties = {
    enabled: true,
    timeout: 30000,
    retryCount: 3,
    logLevel: 'info'
  };

  const typeSpecificProperties: Record<string, Record<string, any>> = {
    // 深度学习节点属性
    'NeuralNetwork': {
      ...commonProperties,
      learningRate: 0.001,
      batchSize: 32,
      epochs: 100,
      optimizer: 'adam',
      lossFunction: 'categorical_crossentropy'
    },
    'ConvolutionalLayer': {
      ...commonProperties,
      filters: 32,
      kernelSize: 3,
      strides: 1,
      padding: 'same',
      activation: 'relu'
    },
    'PoolingLayer': {
      ...commonProperties,
      poolSize: 2,
      strides: 2,
      poolType: 'max'
    },
    
    // 机器学习节点属性
    'LinearRegression': {
      ...commonProperties,
      fitIntercept: true,
      normalize: false,
      copyX: true
    },
    'RandomForest': {
      ...commonProperties,
      nEstimators: 100,
      maxDepth: null,
      minSamplesSplit: 2,
      minSamplesLeaf: 1
    },
    
    // AI工具节点属性
    'ModelDeployment': {
      ...commonProperties,
      deploymentTarget: 'cloud',
      scalingPolicy: 'auto',
      healthCheckInterval: 30
    },
    'ModelMonitoring': {
      ...commonProperties,
      monitoringInterval: 60,
      alertThreshold: 0.1,
      metricsToTrack: ['accuracy', 'latency', 'throughput']
    },
    
    // AI服务节点属性
    'AIInference': {
      ...commonProperties,
      modelId: '',
      inputFormat: 'json',
      outputFormat: 'json',
      maxConcurrency: 10
    },
    'TextClassification': {
      ...commonProperties,
      model: 'bert-base-uncased',
      maxLength: 512,
      numLabels: 2
    }
  };

  return typeSpecificProperties[nodeType] || commonProperties;
};

/**
 * AI节点属性编辑器组件
 */
const AINodePropertyEditor: React.FC<AINodePropertyEditorProps> = ({
  node,
  onPropertyChange,
  onSave,
  visible = true
}) => {
  const [form] = Form.useForm();
  const [properties, setProperties] = useState<Record<string, any>>({});
  const [loading, setLoading] = useState(false);

  // 初始化属性
  useEffect(() => {
    if (node) {
      const defaultProps = getDefaultProperties(node.type);
      setProperties(defaultProps);
      form.setFieldsValue(defaultProps);
    }
  }, [node, form]);

  // 处理属性变更
  const handlePropertyChange = (property: string, value: any) => {
    const newProperties = { ...properties, [property]: value };
    setProperties(newProperties);
    
    if (node && onPropertyChange) {
      onPropertyChange(node.id, property, value);
    }
  };

  // 处理保存
  const handleSave = async () => {
    if (!node) return;
    
    try {
      setLoading(true);
      const values = await form.validateFields();
      
      if (onSave) {
        onSave(node.id, values);
      }
    } catch (error) {
      console.error('保存属性失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 处理重置
  const handleReset = () => {
    if (node) {
      const defaultProps = getDefaultProperties(node.type);
      setProperties(defaultProps);
      form.setFieldsValue(defaultProps);
    }
  };

  // 渲染属性表单项
  const renderPropertyField = (key: string, value: any) => {
    const commonProps = {
      onChange: (val: any) => handlePropertyChange(key, val)
    };

    switch (typeof value) {
      case 'boolean':
        return (
          <Form.Item
            key={key}
            name={key}
            label={key}
            valuePropName="checked"
          >
            <Switch {...commonProps} />
          </Form.Item>
        );
      
      case 'number':
        if (key.includes('Rate') || key.includes('Threshold')) {
          return (
            <Form.Item key={key} name={key} label={key}>
              <Slider
                min={0}
                max={1}
                step={0.001}
                {...commonProps}
              />
            </Form.Item>
          );
        }
        return (
          <Form.Item key={key} name={key} label={key}>
            <InputNumber
              style={{ width: '100%' }}
              {...commonProps}
            />
          </Form.Item>
        );
      
      case 'string':
        if (key.includes('Type') || key.includes('Format') || key.includes('Target')) {
          const options = getSelectOptions(key);
          return (
            <Form.Item key={key} name={key} label={key}>
              <Select {...commonProps}>
                {options.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          );
        }
        
        if (key.includes('Description') || key.includes('Config')) {
          return (
            <Form.Item key={key} name={key} label={key}>
              <TextArea
                rows={3}
                {...commonProps}
              />
            </Form.Item>
          );
        }
        
        return (
          <Form.Item key={key} name={key} label={key}>
            <Input {...commonProps} />
          </Form.Item>
        );
      
      default:
        if (Array.isArray(value)) {
          return (
            <Form.Item key={key} name={key} label={key}>
              <Select
                mode="multiple"
                style={{ width: '100%' }}
                {...commonProps}
              >
                {value.map((item, index) => (
                  <Option key={index} value={item}>
                    {item}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          );
        }
        
        return (
          <Form.Item key={key} name={key} label={key}>
            <Input {...commonProps} />
          </Form.Item>
        );
    }
  };

  // 获取选择框选项
  const getSelectOptions = (key: string) => {
    const optionsMap: Record<string, Array<{ value: string; label: string }>> = {
      optimizer: [
        { value: 'adam', label: 'Adam' },
        { value: 'sgd', label: 'SGD' },
        { value: 'rmsprop', label: 'RMSprop' }
      ],
      activation: [
        { value: 'relu', label: 'ReLU' },
        { value: 'sigmoid', label: 'Sigmoid' },
        { value: 'tanh', label: 'Tanh' },
        { value: 'softmax', label: 'Softmax' }
      ],
      deploymentTarget: [
        { value: 'cloud', label: '云端' },
        { value: 'edge', label: '边缘' },
        { value: 'local', label: '本地' }
      ],
      inputFormat: [
        { value: 'json', label: 'JSON' },
        { value: 'xml', label: 'XML' },
        { value: 'csv', label: 'CSV' }
      ],
      logLevel: [
        { value: 'debug', label: 'Debug' },
        { value: 'info', label: 'Info' },
        { value: 'warn', label: 'Warning' },
        { value: 'error', label: 'Error' }
      ]
    };

    return optionsMap[key] || [];
  };

  if (!visible || !node) {
    return null;
  }

  return (
    <Card
      title={
        <Space>
          {CATEGORY_ICONS[node.category]}
          <Text strong>{node.name} 属性</Text>
          <Tag color="blue">{node.type}</Tag>
        </Space>
      }
      size="small"
      extra={
        <Space>
          <Tooltip title="重置属性">
            <Button
              size="small"
              icon={<ReloadOutlined />}
              onClick={handleReset}
            />
          </Tooltip>
          <Tooltip title="保存属性">
            <Button
              type="primary"
              size="small"
              icon={<SaveOutlined />}
              loading={loading}
              onClick={handleSave}
            />
          </Tooltip>
        </Space>
      }
    >
      <Alert
        message="节点属性配置"
        description={node.description}
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />
      
      <Form
        form={form}
        layout="vertical"
        size="small"
      >
        <Collapse size="small" defaultActiveKey={['basic', 'advanced']}>
          <Panel header="基础属性" key="basic">
            {Object.entries(properties)
              .filter(([key]) => ['enabled', 'timeout', 'retryCount', 'logLevel'].includes(key))
              .map(([key, value]) => renderPropertyField(key, value))
            }
          </Panel>
          
          <Panel header="高级属性" key="advanced">
            {Object.entries(properties)
              .filter(([key]) => !['enabled', 'timeout', 'retryCount', 'logLevel'].includes(key))
              .map(([key, value]) => renderPropertyField(key, value))
            }
          </Panel>
        </Collapse>
      </Form>
    </Card>
  );
};

export default AINodePropertyEditor;
