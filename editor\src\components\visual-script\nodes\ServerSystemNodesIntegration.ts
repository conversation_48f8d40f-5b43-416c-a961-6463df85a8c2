/**
 * 服务器系统节点集成
 * 集成批次4：58个服务器系统节点集成到编辑器
 */

import { NodeEditor } from '../NodeEditor';
import { VisualScriptNode } from '../../../libs/dl-engine-types';
import { ServerNodeItem, ServerNodeCategory } from '../panels/ServerSystemNodesPanel';

/**
 * 服务器系统节点集成类
 */
export class ServerSystemNodesIntegration {
  private nodeEditor: NodeEditor;
  private registeredNodes: Map<string, ServerNodeItem> = new Map();
  private nodeCategories: Map<string, string[]> = new Map();

  constructor(nodeEditor: NodeEditor) {
    this.nodeEditor = nodeEditor;
    this.initializeNodes();
  }

  /**
   * 初始化服务器系统节点
   */
  private initializeNodes(): void {
    console.log('🚀 开始集成批次4：服务器系统节点...');

    this.registerUserServiceNodes();
    this.registerDataServiceNodes();
    this.registerFileServiceNodes();
    this.registerAuthServiceNodes();
    this.registerNotificationServiceNodes();
    this.registerMonitoringServiceNodes();
    this.registerProjectManagementNodes();

    this.setupNodeCategories();
    this.setupNodePalette();

    console.log(`✅ 集成批次4完成，总计: ${this.registeredNodes.size}个服务器系统节点`);
  }

  /**
   * 注册用户服务节点（12个）
   */
  private registerUserServiceNodes(): void {
    const userServiceNodes = [
      'UserRegistrationNode',
      'UserAuthenticationNode',
      'UserProfileNode',
      'UserPermissionNode',
      'UserSessionNode',
      'UserPreferencesNode',
      'UserActivityNode',
      'UserNotificationNode',
      'UserGroupNode',
      'UserRoleNode',
      'UserSecurityNode',
      'UserAnalyticsNode'
    ];

    userServiceNodes.forEach(nodeType => {
      this.registerServerNode(nodeType, ServerNodeCategory.USER_SERVICE, '👤', '#1890ff');
    });

    console.log(`已注册 ${userServiceNodes.length} 个用户服务节点`);
  }

  /**
   * 注册数据服务节点（12个）
   */
  private registerDataServiceNodes(): void {
    const dataServiceNodes = [
      'DatabaseConnectionNode',
      'DatabaseQueryNode',
      'DatabaseInsertNode',
      'DatabaseUpdateNode',
      'DatabaseDeleteNode',
      'DatabaseTransactionNode',
      'DataValidationNode',
      'DataTransformationNode',
      'DataCacheNode',
      'DataBackupNode',
      'DataSyncNode',
      'DataReplicationNode'
    ];

    dataServiceNodes.forEach(nodeType => {
      this.registerServerNode(nodeType, ServerNodeCategory.DATA_SERVICE, '🗄️', '#52c41a');
    });

    console.log(`已注册 ${dataServiceNodes.length} 个数据服务节点`);
  }

  /**
   * 注册文件服务节点（10个）
   */
  private registerFileServiceNodes(): void {
    const fileServiceNodes = [
      'FileUploadNode',
      'FileDownloadNode',
      'FileStorageNode',
      'FileCompressionNode',
      'FileEncryptionNode',
      'FileMetadataNode',
      'FileVersioningNode',
      'FileSearchNode',
      'FileSyncNode',
      'FileCleanupNode'
    ];

    fileServiceNodes.forEach(nodeType => {
      this.registerServerNode(nodeType, ServerNodeCategory.FILE_SERVICE, '📁', '#fa8c16');
    });

    console.log(`已注册 ${fileServiceNodes.length} 个文件服务节点`);
  }

  /**
   * 注册认证授权节点（7个）
   */
  private registerAuthServiceNodes(): void {
    const authServiceNodes = [
      'JWTTokenNode',
      'OAuth2Node',
      'RBACNode',
      'APIKeyNode',
      'SessionManagerNode',
      'PermissionCheckNode',
      'SecurityAuditNode'
    ];

    authServiceNodes.forEach(nodeType => {
      this.registerServerNode(nodeType, ServerNodeCategory.AUTH_SERVICE, '🔐', '#722ed1');
    });

    console.log(`已注册 ${authServiceNodes.length} 个认证授权节点`);
  }

  /**
   * 注册通知服务节点（8个）
   */
  private registerNotificationServiceNodes(): void {
    const notificationServiceNodes = [
      'NotificationServiceNode',
      'EmailNotificationNode',
      'PushNotificationNode',
      'SMSNotificationNode',
      'WebhookNotificationNode',
      'NotificationTemplateNode',
      'NotificationQueueNode',
      'NotificationAnalyticsNode'
    ];

    notificationServiceNodes.forEach(nodeType => {
      this.registerServerNode(nodeType, ServerNodeCategory.NOTIFICATION_SERVICE, '📢', '#eb2f96');
    });

    console.log(`已注册 ${notificationServiceNodes.length} 个通知服务节点`);
  }

  /**
   * 注册监控服务节点（5个）
   */
  private registerMonitoringServiceNodes(): void {
    const monitoringServiceNodes = [
      'SystemMonitoringNode',
      'PerformanceAnalysisNode',
      'AlertManagementNode',
      'LogAnalysisNode',
      'MetricsCollectionNode'
    ];

    monitoringServiceNodes.forEach(nodeType => {
      this.registerServerNode(nodeType, ServerNodeCategory.MONITORING_SERVICE, '📊', '#13c2c2');
    });

    console.log(`已注册 ${monitoringServiceNodes.length} 个监控服务节点`);
  }

  /**
   * 注册项目管理节点（4个）
   */
  private registerProjectManagementNodes(): void {
    const projectManagementNodes = [
      'ProjectManagementNode',
      'TaskManagementNode',
      'ResourceManagementNode',
      'TimeTrackingNode'
    ];

    projectManagementNodes.forEach(nodeType => {
      this.registerServerNode(nodeType, ServerNodeCategory.PROJECT_MANAGEMENT, '📋', '#f759ab');
    });

    console.log(`已注册 ${projectManagementNodes.length} 个项目管理节点`);
  }

  /**
   * 注册服务器节点
   */
  private registerServerNode(
    nodeType: string,
    category: ServerNodeCategory,
    icon: string,
    color: string
  ): void {
    const nodeConfig: ServerNodeItem = {
      id: `${nodeType.toLowerCase()}-${Date.now()}`,
      type: nodeType,
      name: this.getNodeDisplayName(nodeType),
      description: this.getNodeDescription(nodeType),
      category,
      icon,
      color,
      complexity: this.getNodeComplexity(nodeType),
      tags: this.getNodeTags(nodeType),
      inputs: this.getNodeInputs(nodeType),
      outputs: this.getNodeOutputs(nodeType),
      properties: this.getNodeProperties(nodeType),
      version: '1.0.0',
      author: 'DL Engine Team'
    };

    this.registeredNodes.set(nodeType, nodeConfig);

    // 注册到节点编辑器
    if (this.nodeEditor && this.nodeEditor.registerNode) {
      this.nodeEditor.registerNode(nodeType, nodeConfig);
    }
  }

  /**
   * 获取节点显示名称
   */
  private getNodeDisplayName(nodeType: string): string {
    const nameMap: Record<string, string> = {
      'UserRegistrationNode': '用户注册',
      'UserAuthenticationNode': '用户认证',
      'DatabaseConnectionNode': '数据库连接',
      'FileUploadNode': '文件上传',
      'JWTTokenNode': 'JWT令牌',
      'NotificationServiceNode': '通知服务',
      'SystemMonitoringNode': '系统监控',
      'ProjectManagementNode': '项目管理'
    };

    return nameMap[nodeType] || nodeType.replace('Node', '');
  }

  /**
   * 获取节点描述
   */
  private getNodeDescription(nodeType: string): string {
    const descriptionMap: Record<string, string> = {
      'UserRegistrationNode': '处理用户注册请求，验证用户信息并创建账户',
      'UserAuthenticationNode': '验证用户身份，支持多种认证方式',
      'DatabaseConnectionNode': '管理数据库连接和连接池',
      'FileUploadNode': '处理文件上传和存储',
      'JWTTokenNode': '生成和验证JWT令牌',
      'NotificationServiceNode': '统一通知服务管理',
      'SystemMonitoringNode': '监控系统资源使用情况',
      'ProjectManagementNode': '管理项目信息和状态'
    };

    return descriptionMap[nodeType] || `${nodeType} 服务器节点`;
  }

  /**
   * 获取节点复杂度
   */
  private getNodeComplexity(nodeType: string): 'basic' | 'intermediate' | 'advanced' {
    const complexityMap: Record<string, 'basic' | 'intermediate' | 'advanced'> = {
      'UserRegistrationNode': 'basic',
      'UserAuthenticationNode': 'intermediate',
      'UserPermissionNode': 'advanced',
      'DatabaseConnectionNode': 'intermediate',
      'JWTTokenNode': 'intermediate',
      'OAuth2Node': 'advanced',
      'PerformanceAnalysisNode': 'advanced'
    };

    return complexityMap[nodeType] || 'basic';
  }

  /**
   * 获取节点标签
   */
  private getNodeTags(nodeType: string): string[] {
    const tagsMap: Record<string, string[]> = {
      'UserRegistrationNode': ['用户', '注册', '认证'],
      'DatabaseConnectionNode': ['数据库', '连接', '管理'],
      'FileUploadNode': ['文件', '上传', '存储'],
      'JWTTokenNode': ['JWT', '令牌', '认证'],
      'NotificationServiceNode': ['通知', '服务', '管理']
    };

    return tagsMap[nodeType] || ['服务器', '节点'];
  }

  /**
   * 获取节点输入
   */
  private getNodeInputs(nodeType: string): Array<{
    name: string;
    type: string;
    description: string;
    required?: boolean;
  }> {
    // 返回基本输入配置
    return [
      { name: 'input', type: 'any', description: '输入数据', required: true }
    ];
  }

  /**
   * 获取节点输出
   */
  private getNodeOutputs(nodeType: string): Array<{
    name: string;
    type: string;
    description: string;
  }> {
    // 返回基本输出配置
    return [
      { name: 'output', type: 'any', description: '输出数据' },
      { name: 'success', type: 'boolean', description: '操作是否成功' }
    ];
  }

  /**
   * 获取节点属性
   */
  private getNodeProperties(nodeType: string): Array<{
    name: string;
    type: string;
    defaultValue: any;
    description: string;
  }> {
    // 返回基本属性配置
    return [
      { name: 'enabled', type: 'boolean', defaultValue: true, description: '启用节点' },
      { name: 'timeout', type: 'number', defaultValue: 30000, description: '超时时间（毫秒）' }
    ];
  }

  /**
   * 设置节点分类
   */
  private setupNodeCategories(): void {
    this.nodeCategories.set('用户服务', ['UserRegistrationNode', 'UserAuthenticationNode']);
    this.nodeCategories.set('数据服务', ['DatabaseConnectionNode', 'DatabaseQueryNode']);
    this.nodeCategories.set('文件服务', ['FileUploadNode', 'FileDownloadNode']);
    this.nodeCategories.set('认证授权', ['JWTTokenNode', 'OAuth2Node']);
    this.nodeCategories.set('通知服务', ['NotificationServiceNode', 'EmailNotificationNode']);
    this.nodeCategories.set('监控服务', ['SystemMonitoringNode', 'PerformanceAnalysisNode']);
    this.nodeCategories.set('项目管理', ['ProjectManagementNode', 'TaskManagementNode']);
  }

  /**
   * 设置节点面板
   */
  private setupNodePalette(): void {
    if (this.nodeEditor && this.nodeEditor.addNodePalette) {
      this.nodeEditor.addNodePalette('服务器系统节点', Array.from(this.registeredNodes.keys()));
    }
  }

  /**
   * 获取已注册的节点
   */
  public getRegisteredNodes(): Map<string, ServerNodeItem> {
    return this.registeredNodes;
  }

  /**
   * 获取节点分类
   */
  public getNodeCategories(): Map<string, string[]> {
    return this.nodeCategories;
  }

  /**
   * 获取集成统计信息
   */
  public getIntegrationStats() {
    return {
      totalNodes: this.registeredNodes.size,
      categories: this.nodeCategories.size,
      userServiceNodes: 12,
      dataServiceNodes: 12,
      fileServiceNodes: 10,
      authServiceNodes: 7,
      notificationServiceNodes: 8,
      monitoringServiceNodes: 5,
      projectManagementNodes: 4
    };
  }
}

/**
 * 集成服务器系统节点到编辑器
 */
export const integrateServerSystemNodes = (nodeEditor: NodeEditor): ServerSystemNodesIntegration => {
  return new ServerSystemNodesIntegration(nodeEditor);
};

export default ServerSystemNodesIntegration;
