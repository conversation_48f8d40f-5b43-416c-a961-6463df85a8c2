/**
 * AI系统节点面板包装器
 * 集成AI系统面板到编辑器主界面
 */

import React, { useState, useCallback, useEffect } from 'react';
import { Card, Row, Col, message, Spin } from 'antd';
import { useTranslation } from 'react-i18next';
import AISystemNodesPanel, { AINodeItem } from './AISystemNodesPanel';
import AINodePropertyEditor from '../nodes/AINodePropertyEditor';
import { useEngineService } from '../../../hooks/useEngineService';

// 包装器组件属性
interface AISystemNodesPanelWrapperProps {
  visible?: boolean;
  height?: number | string;
  onNodeAdded?: (nodeType: string, nodeData: any) => void;
}

/**
 * AI系统节点面板包装器组件
 */
const AISystemNodesPanelWrapper: React.FC<AISystemNodesPanelWrapperProps> = ({
  visible = true,
  height = '100%',
  onNodeAdded
}) => {
  const { t } = useTranslation();
  const engineService = useEngineService();
  const [selectedNode, setSelectedNode] = useState<AINodeItem | undefined>();
  const [loading, setLoading] = useState(false);
  const [nodeProperties, setNodeProperties] = useState<Record<string, Record<string, any>>>({});

  // 初始化AI系统
  useEffect(() => {
    if (visible) {
      initializeAISystem();
    }
  }, [visible]);

  // 初始化AI系统
  const initializeAISystem = async () => {
    try {
      setLoading(true);
      
      // 检查AI系统是否已初始化
      if (engineService?.isAISystemInitialized?.()) {
        console.log('AI系统已初始化');
        return;
      }

      // 初始化AI系统
      await engineService?.initializeAISystem?.();
      console.log('AI系统初始化完成');
      
    } catch (error) {
      console.error('AI系统初始化失败:', error);
      message.error('AI系统初始化失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理节点选择
  const handleNodeSelect = useCallback((node: AINodeItem) => {
    setSelectedNode(node);
    console.log('选择AI节点:', node);
  }, []);

  // 处理节点添加
  const handleNodeAdd = useCallback(async (nodeType: string) => {
    try {
      setLoading(true);
      
      // 创建节点实例
      const nodeInstance = await engineService?.createVisualScriptNode?.(nodeType);
      
      if (nodeInstance) {
        // 生成唯一ID
        const nodeId = `ai_${nodeType}_${Date.now()}`;
        
        // 创建节点数据
        const nodeData = {
          id: nodeId,
          type: nodeType,
          position: { x: 100, y: 100 },
          properties: nodeProperties[nodeId] || {},
          instance: nodeInstance
        };

        // 通知父组件节点已添加
        onNodeAdded?.(nodeType, nodeData);
        
        message.success(`已添加 ${nodeType} 节点`);
        console.log('添加AI节点:', nodeData);
      } else {
        throw new Error('节点创建失败');
      }
      
    } catch (error) {
      console.error('添加AI节点失败:', error);
      message.error(`添加节点失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setLoading(false);
    }
  }, [engineService, nodeProperties, onNodeAdded]);

  // 处理属性变更
  const handlePropertyChange = useCallback((nodeId: string, property: string, value: any) => {
    setNodeProperties(prev => ({
      ...prev,
      [nodeId]: {
        ...prev[nodeId],
        [property]: value
      }
    }));
    
    console.log('AI节点属性变更:', { nodeId, property, value });
  }, []);

  // 处理属性保存
  const handlePropertySave = useCallback(async (nodeId: string, properties: Record<string, any>) => {
    try {
      setLoading(true);
      
      // 更新节点属性
      setNodeProperties(prev => ({
        ...prev,
        [nodeId]: properties
      }));

      // 如果有引擎服务，更新节点实例属性
      if (engineService?.updateNodeProperties) {
        await engineService.updateNodeProperties(nodeId, properties);
      }
      
      message.success('属性保存成功');
      console.log('保存AI节点属性:', { nodeId, properties });
      
    } catch (error) {
      console.error('保存AI节点属性失败:', error);
      message.error('属性保存失败');
    } finally {
      setLoading(false);
    }
  }, [engineService]);

  // 获取面板样式
  const getPanelStyle = () => ({
    height: typeof height === 'number' ? `${height}px` : height,
    display: visible ? 'block' : 'none'
  });

  return (
    <div style={getPanelStyle()}>
      <Spin spinning={loading} tip="处理中...">
        <Row gutter={[16, 16]} style={{ height: '100%' }}>
          {/* AI节点面板 */}
          <Col xs={24} sm={24} md={14} lg={16} xl={18}>
            <AISystemNodesPanel
              visible={visible}
              onNodeSelect={handleNodeSelect}
              onNodeAdd={handleNodeAdd}
              height="100%"
            />
          </Col>
          
          {/* 属性编辑面板 */}
          <Col xs={24} sm={24} md={10} lg={8} xl={6}>
            <AINodePropertyEditor
              node={selectedNode}
              onPropertyChange={handlePropertyChange}
              onSave={handlePropertySave}
              visible={!!selectedNode}
            />
          </Col>
        </Row>
      </Spin>
    </div>
  );
};

export default AISystemNodesPanelWrapper;
