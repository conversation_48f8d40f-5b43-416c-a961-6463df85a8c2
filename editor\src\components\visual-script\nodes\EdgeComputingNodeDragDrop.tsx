/**
 * 边缘计算节点拖拽功能组件
 * 集成批次5：边缘计算扩展面板（59个节点）拖拽功能实现
 */

import React, { useState, useCallback } from 'react';
import { useDrag, useDrop } from 'react-dnd';
import { Card, Space, Tag, Typography, Tooltip, Button, message } from 'antd';
import {
  CloudServerOutlined,
  DragOutlined,
  InfoCircleOutlined,
  PlusOutlined,
  SettingOutlined
} from '@ant-design/icons';

const { Text } = Typography;

/**
 * 边缘计算节点拖拽项接口
 */
export interface EdgeComputingDragItem {
  type: string;
  name: string;
  description: string;
  category: string;
  icon: string;
  color: string;
  tags: string[];
  nodeData?: any;
}

/**
 * 拖拽组件属性
 */
export interface EdgeComputingNodeDragDropProps {
  node: EdgeComputingDragItem;
  onDragStart?: (node: EdgeComputingDragItem) => void;
  onDragEnd?: (node: EdgeComputingDragItem) => void;
  onNodeAdd?: (node: EdgeComputingDragItem) => void;
  onNodeInfo?: (node: EdgeComputingDragItem) => void;
  disabled?: boolean;
  showActions?: boolean;
}

/**
 * 边缘计算节点拖拽组件
 */
export const EdgeComputingNodeDragDrop: React.FC<EdgeComputingNodeDragDropProps> = ({
  node,
  onDragStart,
  onDragEnd,
  onNodeAdd,
  onNodeInfo,
  disabled = false,
  showActions = true
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  // 配置拖拽
  const [{ opacity }, drag] = useDrag({
    type: 'EDGE_COMPUTING_NODE',
    item: () => {
      setIsDragging(true);
      onDragStart?.(node);
      return {
        ...node,
        dragType: 'EDGE_COMPUTING_NODE'
      };
    },
    end: () => {
      setIsDragging(false);
      onDragEnd?.(node);
    },
    collect: (monitor) => ({
      opacity: monitor.isDragging() ? 0.5 : 1,
    }),
    canDrag: !disabled
  });

  // 处理节点添加
  const handleNodeAdd = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    if (onNodeAdd) {
      onNodeAdd(node);
      message.success(`已添加 ${node.name} 节点到画布`);
    }
  }, [node, onNodeAdd]);

  // 处理节点信息
  const handleNodeInfo = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    if (onNodeInfo) {
      onNodeInfo(node);
    }
  }, [node, onNodeInfo]);

  // 获取分类显示名称
  const getCategoryDisplayName = (category: string): string => {
    const categoryMap: Record<string, string> = {
      'Edge/Routing': '边缘路由',
      'Edge/CloudEdge': '云边协调',
      'Edge/5G': '5G网络',
      'Edge/Device': '边缘设备',
      'Edge/AI': '边缘AI'
    };
    return categoryMap[category] || category;
  };

  return (
    <Card
      ref={drag}
      size="small"
      hoverable={!disabled}
      style={{
        opacity,
        cursor: disabled ? 'not-allowed' : 'grab',
        marginBottom: 8,
        border: isHovered ? `2px solid ${node.color}` : '1px solid #f0f0f0',
        borderRadius: 8,
        transition: 'all 0.2s ease',
        transform: isDragging ? 'scale(1.05)' : 'scale(1)',
        boxShadow: isDragging ? '0 4px 12px rgba(0,0,0,0.15)' : '0 1px 3px rgba(0,0,0,0.1)'
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      bodyStyle={{ padding: '12px' }}
    >
      <Space direction="vertical" style={{ width: '100%' }} size="small">
        {/* 节点头部 */}
        <Space style={{ width: '100%', justifyContent: 'space-between' }}>
          <Space>
            <div
              style={{
                width: 24,
                height: 24,
                borderRadius: '50%',
                backgroundColor: node.color,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                fontSize: '12px'
              }}
            >
              {node.icon}
            </div>
            <Text strong style={{ fontSize: '13px' }}>
              {node.name}
            </Text>
          </Space>
          
          {!disabled && (
            <DragOutlined 
              style={{ 
                color: '#999', 
                cursor: 'grab',
                opacity: isHovered ? 1 : 0.5,
                transition: 'opacity 0.2s'
              }} 
            />
          )}
        </Space>

        {/* 节点描述 */}
        <Text 
          type="secondary" 
          style={{ 
            fontSize: '12px',
            lineHeight: '1.4',
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical',
            overflow: 'hidden'
          }}
        >
          {node.description}
        </Text>

        {/* 标签和分类 */}
        <Space wrap size="small">
          <Tag 
            color={node.color} 
            style={{ fontSize: '11px', margin: 0 }}
          >
            {getCategoryDisplayName(node.category)}
          </Tag>
          {node.tags.slice(0, 2).map((tag, index) => (
            <Tag 
              key={index} 
              size="small" 
              style={{ fontSize: '10px', margin: 0 }}
            >
              {tag}
            </Tag>
          ))}
          {node.tags.length > 2 && (
            <Tag size="small" style={{ fontSize: '10px', margin: 0 }}>
              +{node.tags.length - 2}
            </Tag>
          )}
        </Space>

        {/* 操作按钮 */}
        {showActions && !disabled && (
          <Space style={{ width: '100%', justifyContent: 'flex-end' }} size="small">
            <Tooltip title="查看详情">
              <Button
                type="text"
                size="small"
                icon={<InfoCircleOutlined />}
                onClick={handleNodeInfo}
                style={{ color: node.color }}
              />
            </Tooltip>
            <Tooltip title="添加到画布">
              <Button
                type="text"
                size="small"
                icon={<PlusOutlined />}
                onClick={handleNodeAdd}
                style={{ color: node.color }}
              />
            </Tooltip>
          </Space>
        )}
      </Space>
    </Card>
  );
};

/**
 * 边缘计算节点放置区域组件
 */
export interface EdgeComputingDropZoneProps {
  onDrop?: (item: EdgeComputingDragItem, position: { x: number; y: number }) => void;
  children?: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
}

export const EdgeComputingDropZone: React.FC<EdgeComputingDropZoneProps> = ({
  onDrop,
  children,
  className,
  style
}) => {
  const [{ isOver, canDrop }, drop] = useDrop({
    accept: 'EDGE_COMPUTING_NODE',
    drop: (item: EdgeComputingDragItem, monitor) => {
      const offset = monitor.getClientOffset();
      if (offset && onDrop) {
        onDrop(item, { x: offset.x, y: offset.y });
      }
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
    }),
  });

  const dropZoneStyle: React.CSSProperties = {
    ...style,
    backgroundColor: isOver && canDrop ? 'rgba(24, 144, 255, 0.1)' : 'transparent',
    border: isOver && canDrop ? '2px dashed #1890ff' : 'none',
    transition: 'all 0.2s ease',
    ...style
  };

  return (
    <div
      ref={drop}
      className={className}
      style={dropZoneStyle}
    >
      {children}
    </div>
  );
};

export default EdgeComputingNodeDragDrop;
