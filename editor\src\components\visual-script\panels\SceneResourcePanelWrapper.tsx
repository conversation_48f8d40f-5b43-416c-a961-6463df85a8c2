/**
 * 场景与资源管理面板包装器
 * 集成批次2：用于集成55个场景与资源管理节点到编辑器的面板系统中
 */

import React, { useState, useEffect, useRef } from 'react';
import { Card, message, Spin, Alert } from 'antd';
import { FolderOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import SceneResourcePanel, { SceneResourceNodeItem } from './SceneResourcePanel';
import { 
  SceneResourceNodesIntegration, 
  integrateSceneResourceNodes,
  MockNodeEditor 
} from '../nodes/SceneResourceNodesIntegration';

// 组件属性接口
interface SceneResourcePanelWrapperProps {
  onNodeSelect?: (nodeType: string, nodeConfig: any) => void;
  onNodeAdd?: (nodeType: string, position?: { x: number; y: number }) => void;
  height?: number | string;
  width?: number | string;
}

/**
 * 场景与资源管理面板包装器组件
 */
const SceneResourcePanelWrapper: React.FC<SceneResourcePanelWrapperProps> = ({
  onNodeSelect,
  onNodeAdd,
  height = '100%',
  width = '100%'
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [integration, setIntegration] = useState<SceneResourceNodesIntegration | null>(null);
  const nodeEditorRef = useRef<MockNodeEditor | null>(null);

  /**
   * 创建模拟节点编辑器
   */
  const createMockNodeEditor = (): MockNodeEditor => {
    const registeredNodes = new Map();
    const nodePalettes: any[] = [];

    return {
      addNodeToPalette: (nodeType: string, nodeConfig: any) => {
        console.log(`添加节点到面板: ${nodeType}`, nodeConfig);
      },
      
      addNodePalette: (palette: any) => {
        nodePalettes.push(palette);
        console.log(`添加节点面板: ${palette.category}`, palette);
      },
      
      registerNode: (nodeType: string, nodeClass: any) => {
        registeredNodes.set(nodeType, nodeClass);
        console.log(`注册节点: ${nodeType}`);
      },
      
      createNode: (nodeType: string, position?: { x: number; y: number }) => {
        const NodeClass = registeredNodes.get(nodeType);
        if (NodeClass) {
          const node = new NodeClass();
          console.log(`创建节点: ${nodeType}`, position);
          return node;
        }
        console.warn(`未找到节点类型: ${nodeType}`);
        return null;
      },
      
      getRegisteredNodes: () => {
        return registeredNodes;
      }
    };
  };

  // 初始化场景与资源管理节点集成
  useEffect(() => {
    const initializeIntegration = async () => {
      try {
        setLoading(true);
        setError(null);

        // 创建模拟节点编辑器
        const mockNodeEditor = createMockNodeEditor();
        nodeEditorRef.current = mockNodeEditor;

        // 创建场景与资源管理节点集成
        const sceneResourceIntegration = new SceneResourceNodesIntegration(mockNodeEditor);
        
        // 集成所有场景与资源管理节点
        sceneResourceIntegration.integrateAllNodes();
        
        setIntegration(sceneResourceIntegration);
        
        message.success(`场景与资源管理节点集成成功！已集成 ${sceneResourceIntegration.getRegisteredNodeCount()} 个节点`);
        
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : '未知错误';
        setError(errorMessage);
        message.error(`场景与资源管理节点集成失败: ${errorMessage}`);
        console.error('场景与资源管理节点集成失败:', err);
      } finally {
        setLoading(false);
      }
    };

    initializeIntegration();
  }, []);

  // 处理节点选择
  const handleNodeSelect = (node: SceneResourceNodeItem) => {
    if (onNodeSelect && integration) {
      const nodeConfig = integration.getNodeConfig(node.type);
      if (nodeConfig) {
        onNodeSelect(node.type, nodeConfig);
      }
    }
  };

  // 处理节点添加
  const handleNodeAdd = (nodeType: string) => {
    if (nodeEditorRef.current?.createNode) {
      const position = { x: Math.random() * 400, y: Math.random() * 300 };
      const node = nodeEditorRef.current.createNode(nodeType, position);
      
      if (node && onNodeAdd) {
        onNodeAdd(nodeType, position);
        message.success(`已添加 ${nodeType} 节点到编辑器`);
      }
    }
  };

  if (loading) {
    return (
      <Card
        title={
          <span>
            <FolderOutlined /> 场景与资源管理节点
          </span>
        }
        size="small"
        style={{ height, width }}
        bodyStyle={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center',
          height: 'calc(100% - 57px)'
        }}
      >
        <Spin size="large" tip="正在加载场景与资源管理节点..." />
      </Card>
    );
  }

  if (error) {
    return (
      <Card
        title={
          <span>
            <FolderOutlined /> 场景与资源管理节点
          </span>
        }
        size="small"
        style={{ height, width }}
        bodyStyle={{ padding: '12px' }}
      >
        <Alert
          message="节点集成失败"
          description={error}
          type="error"
          showIcon
          action={
            <button
              onClick={() => window.location.reload()}
              style={{
                border: 'none',
                background: 'none',
                color: '#1890ff',
                cursor: 'pointer',
                textDecoration: 'underline'
              }}
            >
              重新加载
            </button>
          }
        />
      </Card>
    );
  }

  return (
    <SceneResourcePanel
      visible={true}
      onNodeSelect={handleNodeSelect}
      onNodeAdd={handleNodeAdd}
      height={height}
      width={width}
    />
  );
};

export default SceneResourcePanelWrapper;
