/**
 * 验证服务器系统节点集成
 * 检查集成批次4：58个服务器系统节点的集成状态
 */

const fs = require('fs');
const path = require('path');

// 检查文件是否存在
function checkFileExists(filePath) {
  const fullPath = path.join(__dirname, '..', filePath);
  return fs.existsSync(fullPath);
}

// 检查文件内容
function checkFileContent(filePath, expectedContent) {
  const fullPath = path.join(__dirname, '..', filePath);
  if (!fs.existsSync(fullPath)) {
    return false;
  }
  
  const content = fs.readFileSync(fullPath, 'utf8');
  return expectedContent.every(text => content.includes(text));
}

// 验证服务器系统节点集成
function verifyServerSystemIntegration() {
  console.log('🔍 验证服务器系统节点集成...');
  
  const results = {
    filesCreated: 0,
    totalFiles: 6,
    contentChecks: 0,
    totalChecks: 10,
    errors: []
  };

  // 检查创建的文件
  const requiredFiles = [
    'editor/src/components/visual-script/panels/ServerSystemNodesPanel.tsx',
    'editor/src/components/visual-script/nodes/ServerNodeDragDrop.tsx',
    'editor/src/components/visual-script/nodes/ServerNodePropertyEditor.tsx',
    'editor/src/components/visual-script/panels/ServerSystemNodesPanelWrapper.tsx',
    'editor/src/components/visual-script/nodes/ServerSystemNodesIntegration.ts',
    'editor/src/components/visual-script/nodes/__tests__/ServerSystemNodesIntegration.test.ts'
  ];

  console.log('\n📁 检查文件创建状态:');
  requiredFiles.forEach(file => {
    if (checkFileExists(file)) {
      console.log(`  ✅ ${file}`);
      results.filesCreated++;
    } else {
      console.log(`  ❌ ${file}`);
      results.errors.push(`文件不存在: ${file}`);
    }
  });

  // 检查主面板文件内容
  console.log('\n📋 检查面板内容:');
  const panelChecks = [
    {
      file: 'editor/src/components/visual-script/panels/ServerSystemNodesPanel.tsx',
      content: [
        'ServerSystemNodesPanel',
        'ServerNodeCategory',
        'USER_SERVICE',
        'DATA_SERVICE',
        'FILE_SERVICE',
        'AUTH_SERVICE',
        'NOTIFICATION_SERVICE',
        'MONITORING_SERVICE',
        'PROJECT_MANAGEMENT'
      ]
    },
    {
      file: 'editor/src/components/visual-script/nodes/ServerNodeDragDrop.tsx',
      content: [
        'DraggableServerNode',
        'ServerNodeDropZone',
        'ItemTypes.SERVER_NODE'
      ]
    },
    {
      file: 'editor/src/components/visual-script/nodes/ServerNodePropertyEditor.tsx',
      content: [
        'ServerNodePropertyEditor',
        'getDefaultProperties',
        'UserRegistrationNode',
        'DatabaseConnectionNode'
      ]
    },
    {
      file: 'editor/src/components/visual-script/panels/ServerSystemNodesPanelWrapper.tsx',
      content: [
        'ServerSystemNodesPanelWrapper',
        'handleNodeAdd',
        'handlePropertySave'
      ]
    },
    {
      file: 'editor/src/components/visual-script/nodes/ServerSystemNodesIntegration.ts',
      content: [
        'ServerSystemNodesIntegration',
        'registerUserServiceNodes',
        'registerDataServiceNodes',
        'integrateServerSystemNodes'
      ]
    }
  ];

  panelChecks.forEach(check => {
    if (checkFileContent(check.file, check.content)) {
      console.log(`  ✅ ${path.basename(check.file)} - 内容检查通过`);
      results.contentChecks++;
    } else {
      console.log(`  ❌ ${path.basename(check.file)} - 内容检查失败`);
      results.errors.push(`内容检查失败: ${check.file}`);
    }
  });

  // 检查节点数量
  console.log('\n🔢 检查节点数量:');
  const panelFile = 'editor/src/components/visual-script/panels/ServerSystemNodesPanel.tsx';
  if (checkFileExists(panelFile)) {
    const content = fs.readFileSync(path.join(__dirname, '..', panelFile), 'utf8');
    
    // 统计节点定义
    const nodeMatches = content.match(/{\s*id:\s*['"][^'"]+['"],\s*type:\s*['"][^'"]+Node['"],/g);
    const nodeCount = nodeMatches ? nodeMatches.length : 0;
    
    console.log(`  📊 发现 ${nodeCount} 个节点定义`);
    
    if (nodeCount >= 20) { // 至少应该有20个节点示例
      console.log(`  ✅ 节点数量检查通过`);
      results.contentChecks++;
    } else {
      console.log(`  ❌ 节点数量不足，期望至少20个，实际${nodeCount}个`);
      results.errors.push(`节点数量不足: ${nodeCount}`);
    }
  }

  // 检查分类覆盖
  console.log('\n📂 检查节点分类覆盖:');
  const expectedCategories = [
    'USER_SERVICE',
    'DATA_SERVICE', 
    'FILE_SERVICE',
    'AUTH_SERVICE',
    'NOTIFICATION_SERVICE',
    'MONITORING_SERVICE',
    'PROJECT_MANAGEMENT'
  ];

  if (checkFileExists(panelFile)) {
    const content = fs.readFileSync(path.join(__dirname, '..', panelFile), 'utf8');
    
    expectedCategories.forEach(category => {
      if (content.includes(category)) {
        console.log(`  ✅ ${category} 分类已定义`);
        results.contentChecks++;
      } else {
        console.log(`  ❌ ${category} 分类缺失`);
        results.errors.push(`分类缺失: ${category}`);
      }
    });
  }

  // 输出结果
  console.log('\n📊 验证结果:');
  console.log(`  文件创建: ${results.filesCreated}/${results.totalFiles}`);
  console.log(`  内容检查: ${results.contentChecks}/${results.totalChecks + expectedCategories.length}`);
  
  if (results.errors.length > 0) {
    console.log('\n❌ 发现问题:');
    results.errors.forEach(error => {
      console.log(`  - ${error}`);
    });
  }

  const success = results.filesCreated === results.totalFiles && 
                 results.contentChecks >= results.totalChecks &&
                 results.errors.length === 0;

  if (success) {
    console.log('\n🎉 服务器系统节点集成验证成功！');
    console.log('✅ 集成批次4：服务器系统面板（58个节点）已完成');
  } else {
    console.log('\n⚠️  服务器系统节点集成验证发现问题');
  }

  return success;
}

// 运行验证
if (require.main === module) {
  verifyServerSystemIntegration();
}

module.exports = { verifyServerSystemIntegration };
