/**
 * AI系统节点面板演示
 * 展示AI系统面板的功能和使用方法
 */

import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Button, Space, Typography, Alert, Divider, Statistic } from 'antd';
import {
  RobotOutlined,
  PlayCircleOutlined,
  StopOutlined,
  ReloadOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import AISystemNodesPanelWrapper from '../panels/AISystemNodesPanelWrapper';
import { aiSystemNodesIntegration } from '../nodes/AISystemNodesIntegration';

const { Title, Text, Paragraph } = Typography;

/**
 * AI系统节点面板演示组件
 */
const AISystemNodesPanelDemo: React.FC = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [integrationStatus, setIntegrationStatus] = useState({
    isInitialized: false,
    totalNodes: 0,
    nodesByCategory: {}
  });
  const [addedNodes, setAddedNodes] = useState<Array<{ type: string; data: any }>>([]);

  // 初始化演示
  useEffect(() => {
    initializeDemo();
  }, []);

  // 初始化演示
  const initializeDemo = async () => {
    try {
      await aiSystemNodesIntegration.initialize();
      updateIntegrationStatus();
    } catch (error) {
      console.error('演示初始化失败:', error);
    }
  };

  // 更新集成状态
  const updateIntegrationStatus = () => {
    const status = aiSystemNodesIntegration.getIntegrationStatus();
    setIntegrationStatus(status);
  };

  // 开始演示
  const startDemo = () => {
    setIsRunning(true);
    console.log('AI系统节点面板演示开始');
  };

  // 停止演示
  const stopDemo = () => {
    setIsRunning(false);
    setAddedNodes([]);
    console.log('AI系统节点面板演示停止');
  };

  // 重置演示
  const resetDemo = () => {
    setIsRunning(false);
    setAddedNodes([]);
    aiSystemNodesIntegration.reset();
    initializeDemo();
    console.log('AI系统节点面板演示重置');
  };

  // 处理节点添加
  const handleNodeAdded = (nodeType: string, nodeData: any) => {
    const newNode = { type: nodeType, data: nodeData };
    setAddedNodes(prev => [...prev, newNode]);
    console.log('演示中添加节点:', newNode);
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <div style={{ marginBottom: '24px' }}>
          <Title level={2}>
            <Space>
              <RobotOutlined style={{ color: '#1890ff' }} />
              AI系统节点面板演示
            </Space>
          </Title>
          <Paragraph>
            这个演示展示了AI系统节点面板的功能，包括82个AI相关节点的分类展示、
            拖拽添加、属性编辑等功能。面板包含6个主要分类：深度学习、机器学习、
            AI工具、AI服务、自然语言处理和模型管理。
          </Paragraph>
        </div>

        {/* 控制面板 */}
        <Card size="small" title="演示控制" style={{ marginBottom: '16px' }}>
          <Space>
            <Button
              type="primary"
              icon={<PlayCircleOutlined />}
              onClick={startDemo}
              disabled={isRunning || !integrationStatus.isInitialized}
            >
              开始演示
            </Button>
            <Button
              icon={<StopOutlined />}
              onClick={stopDemo}
              disabled={!isRunning}
            >
              停止演示
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={resetDemo}
            >
              重置演示
            </Button>
          </Space>
        </Card>

        {/* 状态信息 */}
        <Row gutter={[16, 16]} style={{ marginBottom: '16px' }}>
          <Col xs={24} sm={12} md={6}>
            <Card size="small">
              <Statistic
                title="集成状态"
                value={integrationStatus.isInitialized ? '已初始化' : '未初始化'}
                valueStyle={{ color: integrationStatus.isInitialized ? '#3f8600' : '#cf1322' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card size="small">
              <Statistic
                title="总节点数"
                value={integrationStatus.totalNodes}
                suffix="个"
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card size="small">
              <Statistic
                title="已添加节点"
                value={addedNodes.length}
                suffix="个"
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card size="small">
              <Statistic
                title="演示状态"
                value={isRunning ? '运行中' : '已停止'}
                valueStyle={{ color: isRunning ? '#3f8600' : '#8c8c8c' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 分类统计 */}
        {integrationStatus.isInitialized && (
          <Card size="small" title="节点分类统计" style={{ marginBottom: '16px' }}>
            <Row gutter={[16, 8]}>
              {Object.entries(integrationStatus.nodesByCategory).map(([category, count]) => (
                <Col xs={12} sm={8} md={4} key={category}>
                  <Statistic
                    title={getCategoryDisplayName(category)}
                    value={count as number}
                    suffix="个"
                    valueStyle={{ fontSize: '14px' }}
                  />
                </Col>
              ))}
            </Row>
          </Card>
        )}

        {/* 使用说明 */}
        <Alert
          message="使用说明"
          description={
            <div>
              <p>1. 点击"开始演示"按钮启动AI系统节点面板</p>
              <p>2. 在左侧面板中浏览不同分类的AI节点</p>
              <p>3. 点击节点可以查看详细信息</p>
              <p>4. 拖拽节点或点击"+"按钮添加到画布</p>
              <p>5. 在右侧面板中编辑节点属性</p>
              <p>6. 使用搜索功能快速找到需要的节点</p>
            </div>
          }
          type="info"
          showIcon
          icon={<InfoCircleOutlined />}
          style={{ marginBottom: '16px' }}
        />

        {/* AI系统面板 */}
        {isRunning && (
          <Card title="AI系统节点面板" style={{ marginBottom: '16px' }}>
            <AISystemNodesPanelWrapper
              visible={isRunning}
              height={600}
              onNodeAdded={handleNodeAdded}
            />
          </Card>
        )}

        {/* 已添加节点列表 */}
        {addedNodes.length > 0 && (
          <Card size="small" title="已添加的节点">
            <div style={{ maxHeight: '200px', overflow: 'auto' }}>
              {addedNodes.map((node, index) => (
                <div key={index} style={{ marginBottom: '8px', padding: '8px', border: '1px solid #f0f0f0', borderRadius: '4px' }}>
                  <Space>
                    <Text strong>{node.type}</Text>
                    <Text type="secondary">ID: {node.data.id}</Text>
                    <Text type="secondary">位置: ({node.data.position.x}, {node.data.position.y})</Text>
                  </Space>
                </div>
              ))}
            </div>
          </Card>
        )}

        <Divider />

        {/* 技术信息 */}
        <Card size="small" title="技术信息">
          <Row gutter={[16, 8]}>
            <Col xs={24} sm={12}>
              <Text strong>面板组件:</Text>
              <br />
              <Text code>AISystemNodesPanel.tsx</Text>
            </Col>
            <Col xs={24} sm={12}>
              <Text strong>拖拽组件:</Text>
              <br />
              <Text code>AINodeDragDrop.tsx</Text>
            </Col>
            <Col xs={24} sm={12}>
              <Text strong>属性编辑器:</Text>
              <br />
              <Text code>AINodePropertyEditor.tsx</Text>
            </Col>
            <Col xs={24} sm={12}>
              <Text strong>集成模块:</Text>
              <br />
              <Text code>AISystemNodesIntegration.ts</Text>
            </Col>
          </Row>
        </Card>
      </Card>
    </div>
  );
};

// 获取分类显示名称
const getCategoryDisplayName = (category: string): string => {
  const categoryNames: Record<string, string> = {
    deepLearning: '深度学习',
    machineLearning: '机器学习',
    aiTools: 'AI工具',
    aiServices: 'AI服务',
    naturalLanguageProcessing: '自然语言处理',
    modelManagement: '模型管理'
  };
  return categoryNames[category] || category;
};

export default AISystemNodesPanelDemo;
