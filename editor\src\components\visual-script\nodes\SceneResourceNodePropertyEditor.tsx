/**
 * 场景与资源管理节点属性编辑器组件
 * 提供节点属性的可视化编辑界面
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Form,
  Input,
  InputNumber,
  Select,
  Switch,
  Slider,
  ColorPicker,
  Button,
  Space,
  Divider,
  Typography,
  Tooltip,
  Row,
  Col,
  Collapse,
  Tag,
  Alert
} from 'antd';
import {
  SettingOutlined,
  EyeOutlined,
  SaveOutlined,
  UndoOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { SceneResourceNodeItem } from '../panels/SceneResourcePanel';

const { Panel } = Collapse;
const { Text, Title } = Typography;
const { TextArea } = Input;
const { Option } = Select;

// 属性类型定义
interface NodeProperty {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'select' | 'color' | 'vector3' | 'file' | 'range';
  label: string;
  description?: string;
  value: any;
  defaultValue?: any;
  options?: Array<{ label: string; value: any }>;
  min?: number;
  max?: number;
  step?: number;
  required?: boolean;
  disabled?: boolean;
}

// 组件属性接口
interface SceneResourceNodePropertyEditorProps {
  node?: SceneResourceNodeItem;
  properties?: NodeProperty[];
  onPropertyChange?: (propertyName: string, value: any) => void;
  onPreview?: () => void;
  onSave?: () => void;
  onReset?: () => void;
  visible?: boolean;
}

/**
 * 场景与资源管理节点属性编辑器组件
 */
const SceneResourceNodePropertyEditor: React.FC<SceneResourceNodePropertyEditorProps> = ({
  node,
  properties = [],
  onPropertyChange,
  onPreview,
  onSave,
  onReset,
  visible = true
}) => {
  const [form] = Form.useForm();
  const [currentProperties, setCurrentProperties] = useState<Record<string, any>>({});
  const [hasChanges, setHasChanges] = useState(false);
  const [previewEnabled, setPreviewEnabled] = useState(true);

  // 初始化属性值
  useEffect(() => {
    const initialProperties: Record<string, any> = {};
    properties.forEach(prop => {
      initialProperties[prop.name] = prop.value ?? prop.defaultValue;
    });
    setCurrentProperties(initialProperties);
    form.setFieldsValue(initialProperties);
    setHasChanges(false);
  }, [node, properties, form]);

  // 处理属性值变化
  const handlePropertyChange = useCallback((propertyName: string, value: any) => {
    const newProperties = { ...currentProperties, [propertyName]: value };
    setCurrentProperties(newProperties);
    setHasChanges(true);

    // 实时预览
    if (previewEnabled && onPropertyChange) {
      onPropertyChange(propertyName, value);
    }
  }, [currentProperties, previewEnabled, onPropertyChange]);

  // 保存属性
  const handleSave = useCallback(() => {
    if (onSave) {
      onSave();
      setHasChanges(false);
    }
  }, [onSave]);

  // 重置属性
  const handleReset = useCallback(() => {
    const resetProperties: Record<string, any> = {};
    properties.forEach(prop => {
      resetProperties[prop.name] = prop.defaultValue;
    });
    setCurrentProperties(resetProperties);
    form.setFieldsValue(resetProperties);
    setHasChanges(false);
    
    if (onReset) {
      onReset();
    }
  }, [properties, form, onReset]);

  // 渲染属性编辑控件
  const renderPropertyControl = useCallback((property: NodeProperty) => {
    const { name, type, label, description, options, min, max, step, required, disabled } = property;
    const value = currentProperties[name];

    const commonProps = {
      value,
      onChange: (val: any) => handlePropertyChange(name, val),
      disabled,
      style: { width: '100%' }
    };

    switch (type) {
      case 'string':
        return (
          <Input
            {...commonProps}
            placeholder={`请输入${label}`}
          />
        );

      case 'number':
        return (
          <InputNumber
            {...commonProps}
            min={min}
            max={max}
            step={step}
            placeholder={`请输入${label}`}
          />
        );

      case 'boolean':
        return (
          <Switch
            checked={value}
            onChange={(checked) => handlePropertyChange(name, checked)}
            disabled={disabled}
          />
        );

      case 'select':
        return (
          <Select
            {...commonProps}
            placeholder={`请选择${label}`}
          >
            {options?.map(option => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        );

      case 'color':
        return (
          <ColorPicker
            value={value}
            onChange={(color) => handlePropertyChange(name, color.toHexString())}
            disabled={disabled}
            showText
          />
        );

      case 'range':
        return (
          <Slider
            {...commonProps}
            min={min}
            max={max}
            step={step}
            marks={min !== undefined && max !== undefined ? {
              [min]: min.toString(),
              [max]: max.toString()
            } : undefined}
          />
        );

      case 'vector3':
        const vector = value || { x: 0, y: 0, z: 0 };
        return (
          <Row gutter={8}>
            <Col span={8}>
              <InputNumber
                value={vector.x}
                onChange={(x) => handlePropertyChange(name, { ...vector, x })}
                placeholder="X"
                style={{ width: '100%' }}
                disabled={disabled}
              />
            </Col>
            <Col span={8}>
              <InputNumber
                value={vector.y}
                onChange={(y) => handlePropertyChange(name, { ...vector, y })}
                placeholder="Y"
                style={{ width: '100%' }}
                disabled={disabled}
              />
            </Col>
            <Col span={8}>
              <InputNumber
                value={vector.z}
                onChange={(z) => handlePropertyChange(name, { ...vector, z })}
                placeholder="Z"
                style={{ width: '100%' }}
                disabled={disabled}
              />
            </Col>
          </Row>
        );

      case 'file':
        return (
          <Input
            {...commonProps}
            placeholder={`请选择${label}文件`}
            addonAfter={
              <Button size="small" type="link">
                浏览
              </Button>
            }
          />
        );

      default:
        return (
          <Input
            {...commonProps}
            placeholder={`请输入${label}`}
          />
        );
    }
  }, [currentProperties, handlePropertyChange]);

  if (!visible || !node) {
    return null;
  }

  return (
    <Card
      title={
        <Space>
          <SettingOutlined />
          <Title level={5} style={{ margin: 0 }}>
            节点属性编辑器
          </Title>
          {hasChanges && (
            <Tag color="orange" size="small">
              未保存
            </Tag>
          )}
        </Space>
      }
      size="small"
      style={{ height: '100%' }}
      bodyStyle={{ padding: '12px', height: 'calc(100% - 57px)', overflow: 'auto' }}
      extra={
        <Space size="small">
          <Tooltip title="实时预览">
            <Switch
              size="small"
              checked={previewEnabled}
              onChange={setPreviewEnabled}
              checkedChildren={<EyeOutlined />}
              unCheckedChildren={<EyeOutlined />}
            />
          </Tooltip>
        </Space>
      }
    >
      {/* 节点信息 */}
      <Alert
        message={
          <Space direction="vertical" size="small">
            <Text strong>{node.name}</Text>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {node.description}
            </Text>
            <Space size="small">
              <Tag color="blue" size="small">{node.type}</Tag>
              <Tag color="green" size="small">{node.complexity}</Tag>
            </Space>
          </Space>
        }
        type="info"
        showIcon={false}
        style={{ marginBottom: '16px' }}
      />

      {/* 属性编辑表单 */}
      <Form
        form={form}
        layout="vertical"
        size="small"
        onValuesChange={(changedValues) => {
          Object.entries(changedValues).forEach(([key, value]) => {
            handlePropertyChange(key, value);
          });
        }}
      >
        {properties.length > 0 ? (
          <Collapse size="small" defaultActiveKey={['basic']}>
            <Panel header="基础属性" key="basic">
              {properties.map(property => (
                <Form.Item
                  key={property.name}
                  label={
                    <Space size="small">
                      <Text>{property.label}</Text>
                      {property.required && <Text type="danger">*</Text>}
                      {property.description && (
                        <Tooltip title={property.description}>
                          <InfoCircleOutlined style={{ color: '#999' }} />
                        </Tooltip>
                      )}
                    </Space>
                  }
                  name={property.name}
                  rules={[
                    {
                      required: property.required,
                      message: `请输入${property.label}`
                    }
                  ]}
                >
                  {renderPropertyControl(property)}
                </Form.Item>
              ))}
            </Panel>
          </Collapse>
        ) : (
          <Alert
            message="该节点暂无可编辑属性"
            type="info"
            showIcon
          />
        )}
      </Form>

      {/* 操作按钮 */}
      <Divider style={{ margin: '16px 0' }} />
      <Space style={{ width: '100%', justifyContent: 'center' }}>
        <Button
          type="primary"
          icon={<SaveOutlined />}
          onClick={handleSave}
          disabled={!hasChanges}
          size="small"
        >
          保存
        </Button>
        <Button
          icon={<UndoOutlined />}
          onClick={handleReset}
          disabled={!hasChanges}
          size="small"
        >
          重置
        </Button>
        {onPreview && (
          <Button
            icon={<EyeOutlined />}
            onClick={onPreview}
            size="small"
          >
            预览
          </Button>
        )}
      </Space>
    </Card>
  );
};

export default SceneResourceNodePropertyEditor;
