/**
 * 场景与资源管理面板使用示例
 * 演示如何在编辑器中使用55个场景与资源管理节点
 */

import React, { useState, useCallback } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { Layout, Card, Row, Col, message, Typography, Space, Button } from 'antd';
import { 
  PlayCircleOutlined, 
  PauseCircleOutlined, 
  ReloadOutlined,
  InfoCircleOutlined 
} from '@ant-design/icons';

import SceneResourcePanelWrapper from './SceneResourcePanelWrapper';
import SceneResourceNodePropertyEditor from '../nodes/SceneResourceNodePropertyEditor';
import { SceneResourceDropZone } from '../nodes/SceneResourceNodeDragDrop';
import { SceneResourceNodeItem } from './SceneResourcePanel';

const { Sider, Content } = Layout;
const { Title, Text } = Typography;

/**
 * 场景与资源管理面板使用示例组件
 */
const SceneResourcePanelExample: React.FC = () => {
  const [selectedNode, setSelectedNode] = useState<SceneResourceNodeItem | null>(null);
  const [canvasNodes, setCanvasNodes] = useState<Array<{
    id: string;
    type: string;
    position: { x: number; y: number };
    properties: Record<string, any>;
  }>>([]);
  const [isPlaying, setIsPlaying] = useState(false);

  // 处理节点选择
  const handleNodeSelect = useCallback((nodeType: string, nodeConfig: any) => {
    console.log('选择节点:', nodeType, nodeConfig);
    
    // 创建节点项用于属性编辑
    const nodeItem: SceneResourceNodeItem = {
      id: nodeConfig.id || nodeType,
      name: nodeConfig.name || nodeType,
      type: nodeType,
      category: nodeConfig.category,
      description: nodeConfig.description || '',
      complexity: nodeConfig.complexity || 'intermediate',
      tags: nodeConfig.tags || [],
      inputs: nodeConfig.inputs,
      outputs: nodeConfig.outputs,
      properties: nodeConfig.properties
    };
    
    setSelectedNode(nodeItem);
    message.info(`已选择节点: ${nodeConfig.name || nodeType}`);
  }, []);

  // 处理节点添加到画布
  const handleNodeAdd = useCallback((nodeType: string, position?: { x: number; y: number }) => {
    const newNode = {
      id: `${nodeType}_${Date.now()}`,
      type: nodeType,
      position: position || { x: Math.random() * 400, y: Math.random() * 300 },
      properties: {}
    };

    setCanvasNodes(prev => [...prev, newNode]);
    message.success(`已添加 ${nodeType} 节点到画布`);
  }, []);

  // 处理节点拖拽到画布
  const handleNodeDrop = useCallback((node: SceneResourceNodeItem, position: { x: number; y: number }) => {
    const newNode = {
      id: `${node.type}_${Date.now()}`,
      type: node.type,
      position,
      properties: {}
    };

    setCanvasNodes(prev => [...prev, newNode]);
    message.success(`已将 ${node.name} 拖拽到画布`);
  }, []);

  // 处理属性变化
  const handlePropertyChange = useCallback((propertyName: string, value: any) => {
    console.log('属性变化:', propertyName, value);
    message.info(`属性 ${propertyName} 已更新`);
  }, []);

  // 播放/暂停
  const handlePlayPause = useCallback(() => {
    setIsPlaying(!isPlaying);
    message.info(isPlaying ? '已暂停' : '开始播放');
  }, [isPlaying]);

  // 重置画布
  const handleReset = useCallback(() => {
    setCanvasNodes([]);
    setSelectedNode(null);
    message.info('画布已重置');
  }, []);

  return (
    <DndProvider backend={HTML5Backend}>
      <Layout style={{ height: '100vh' }}>
        {/* 左侧节点面板 */}
        <Sider width={350} style={{ background: '#fff', borderRight: '1px solid #f0f0f0' }}>
          <div style={{ height: '100%', padding: '8px' }}>
            <SceneResourcePanelWrapper
              onNodeSelect={handleNodeSelect}
              onNodeAdd={handleNodeAdd}
              height="100%"
            />
          </div>
        </Sider>

        {/* 中间画布区域 */}
        <Content style={{ background: '#f5f5f5', position: 'relative' }}>
          <div style={{ padding: '16px', height: '100%' }}>
            {/* 工具栏 */}
            <Card size="small" style={{ marginBottom: '16px' }}>
              <Row justify="space-between" align="middle">
                <Col>
                  <Space>
                    <Title level={4} style={{ margin: 0 }}>
                      场景与资源管理节点编辑器
                    </Title>
                    <Text type="secondary">
                      画布节点数: {canvasNodes.length}
                    </Text>
                  </Space>
                </Col>
                <Col>
                  <Space>
                    <Button
                      type={isPlaying ? 'default' : 'primary'}
                      icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                      onClick={handlePlayPause}
                    >
                      {isPlaying ? '暂停' : '播放'}
                    </Button>
                    <Button
                      icon={<ReloadOutlined />}
                      onClick={handleReset}
                    >
                      重置
                    </Button>
                    <Button
                      icon={<InfoCircleOutlined />}
                      onClick={() => {
                        message.info('拖拽左侧节点到此区域，或点击节点的添加按钮');
                      }}
                    >
                      帮助
                    </Button>
                  </Space>
                </Col>
              </Row>
            </Card>

            {/* 画布区域 */}
            <SceneResourceDropZone onNodeDrop={handleNodeDrop}>
              <Card
                style={{ 
                  height: 'calc(100% - 80px)',
                  background: '#fafafa',
                  border: '2px dashed #d9d9d9'
                }}
                bodyStyle={{ 
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  alignItems: 'center',
                  position: 'relative'
                }}
              >
                {canvasNodes.length === 0 ? (
                  <div style={{ textAlign: 'center' }}>
                    <Text type="secondary" style={{ fontSize: '16px' }}>
                      拖拽节点到此处开始构建场景
                    </Text>
                    <br />
                    <Text type="secondary">
                      支持55个场景与资源管理节点
                    </Text>
                  </div>
                ) : (
                  <div style={{ width: '100%', height: '100%', position: 'relative' }}>
                    {canvasNodes.map(node => (
                      <div
                        key={node.id}
                        style={{
                          position: 'absolute',
                          left: node.position.x,
                          top: node.position.y,
                          background: '#fff',
                          border: '1px solid #d9d9d9',
                          borderRadius: '4px',
                          padding: '8px 12px',
                          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                          cursor: 'pointer',
                          minWidth: '120px',
                          textAlign: 'center'
                        }}
                        onClick={() => {
                          // 这里可以添加节点选择逻辑
                          message.info(`选择了画布节点: ${node.type}`);
                        }}
                      >
                        <Text strong>{node.type}</Text>
                        <br />
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {node.id}
                        </Text>
                      </div>
                    ))}
                  </div>
                )}
              </Card>
            </SceneResourceDropZone>
          </div>
        </Content>

        {/* 右侧属性面板 */}
        <Sider width={300} style={{ background: '#fff', borderLeft: '1px solid #f0f0f0' }}>
          <div style={{ height: '100%', padding: '8px' }}>
            <SceneResourceNodePropertyEditor
              node={selectedNode}
              properties={selectedNode?.properties?.map(prop => ({
                name: prop.name,
                type: prop.type as any,
                label: prop.name,
                description: prop.description,
                value: prop.defaultValue,
                defaultValue: prop.defaultValue,
                options: prop.options
              })) || []}
              onPropertyChange={handlePropertyChange}
              onPreview={() => message.info('预览功能')}
              onSave={() => message.success('属性已保存')}
              onReset={() => message.info('属性已重置')}
              visible={!!selectedNode}
            />
            
            {!selectedNode && (
              <Card style={{ height: '100%' }}>
                <div style={{ 
                  height: '100%', 
                  display: 'flex', 
                  justifyContent: 'center', 
                  alignItems: 'center',
                  textAlign: 'center'
                }}>
                  <div>
                    <InfoCircleOutlined style={{ fontSize: '48px', color: '#d9d9d9', marginBottom: '16px' }} />
                    <br />
                    <Text type="secondary">
                      选择一个节点查看其属性
                    </Text>
                  </div>
                </div>
              </Card>
            )}
          </div>
        </Sider>
      </Layout>
    </DndProvider>
  );
};

export default SceneResourcePanelExample;
