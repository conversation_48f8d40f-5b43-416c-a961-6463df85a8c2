/**
 * 服务器节点属性编辑器
 * 用于编辑服务器系统节点的属性和配置
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  InputNumber,
  Switch,
  Select,
  Button,
  Space,
  Divider,
  Typography,
  Collapse,
  Tag,
  Tooltip,
  Alert
} from 'antd';
import {
  SaveOutlined,
  ReloadOutlined,
  InfoCircleOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { ServerNodeItem } from '../panels/ServerSystemNodesPanel';

const { Text, Title } = Typography;
const { Panel } = Collapse;
const { Option } = Select;
const { TextArea } = Input;

// 属性编辑器组件属性
interface ServerNodePropertyEditorProps {
  node?: ServerNodeItem;
  onPropertyChange?: (nodeId: string, property: string, value: any) => void;
  onSave?: (nodeId: string, properties: Record<string, any>) => void;
  visible?: boolean;
}

// 获取节点默认属性
const getDefaultProperties = (nodeType: string): Record<string, any> => {
  const commonProperties = {
    enabled: true,
    timeout: 30000,
    retryCount: 3,
    logLevel: 'info'
  };

  switch (nodeType) {
    case 'UserRegistrationNode':
      return {
        ...commonProperties,
        enableEmailVerification: true,
        passwordMinLength: 8,
        allowDuplicateEmail: false
      };
    case 'UserAuthenticationNode':
      return {
        ...commonProperties,
        tokenExpiry: 3600,
        maxAttempts: 3,
        lockoutDuration: 300
      };
    case 'DatabaseConnectionNode':
      return {
        ...commonProperties,
        maxConnections: 10,
        connectionTimeout: 30000,
        idleTimeout: 600000
      };
    case 'FileUploadNode':
      return {
        ...commonProperties,
        maxFileSize: 10485760,
        allowedTypes: ['*'],
        enableVirusScan: true
      };
    case 'JWTTokenNode':
      return {
        ...commonProperties,
        algorithm: 'HS256',
        expiresIn: '1h',
        issuer: 'dl-engine'
      };
    default:
      return commonProperties;
  }
};

/**
 * 服务器节点属性编辑器组件
 */
const ServerNodePropertyEditor: React.FC<ServerNodePropertyEditorProps> = ({
  node,
  onPropertyChange,
  onSave,
  visible = true
}) => {
  const [form] = Form.useForm();
  const [properties, setProperties] = useState<Record<string, any>>({});
  const [loading, setLoading] = useState(false);

  // 初始化属性
  useEffect(() => {
    if (node) {
      const defaultProps = getDefaultProperties(node.type);
      setProperties(defaultProps);
      form.setFieldsValue(defaultProps);
    }
  }, [node, form]);

  // 处理属性变更
  const handlePropertyChange = (property: string, value: any) => {
    const newProperties = { ...properties, [property]: value };
    setProperties(newProperties);
    
    if (node && onPropertyChange) {
      onPropertyChange(node.id, property, value);
    }
  };

  // 处理保存
  const handleSave = async () => {
    if (!node) return;
    
    try {
      setLoading(true);
      const values = await form.validateFields();
      
      if (onSave) {
        onSave(node.id, values);
      }
    } catch (error) {
      console.error('保存属性失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 处理重置
  const handleReset = () => {
    if (node) {
      const defaultProps = getDefaultProperties(node.type);
      setProperties(defaultProps);
      form.setFieldsValue(defaultProps);
    }
  };

  // 渲染属性表单项
  const renderPropertyField = (key: string, value: any) => {
    const commonProps = {
      onChange: (val: any) => handlePropertyChange(key, val)
    };

    if (typeof value === 'boolean') {
      return (
        <Form.Item
          key={key}
          name={key}
          label={key}
          valuePropName="checked"
        >
          <Switch {...commonProps} />
        </Form.Item>
      );
    }

    if (typeof value === 'number') {
      return (
        <Form.Item
          key={key}
          name={key}
          label={key}
        >
          <InputNumber
            style={{ width: '100%' }}
            {...commonProps}
          />
        </Form.Item>
      );
    }

    if (Array.isArray(value)) {
      return (
        <Form.Item
          key={key}
          name={key}
          label={key}
        >
          <Select
            mode="tags"
            style={{ width: '100%' }}
            placeholder="输入并回车添加"
            {...commonProps}
          >
            {value.map((item, index) => (
              <Option key={index} value={item}>
                {item}
              </Option>
            ))}
          </Select>
        </Form.Item>
      );
    }

    if (key.toLowerCase().includes('password') || key.toLowerCase().includes('secret')) {
      return (
        <Form.Item
          key={key}
          name={key}
          label={key}
        >
          <Input.Password {...commonProps} />
        </Form.Item>
      );
    }

    if (typeof value === 'string' && value.length > 50) {
      return (
        <Form.Item
          key={key}
          name={key}
          label={key}
        >
          <TextArea
            rows={3}
            {...commonProps}
          />
        </Form.Item>
      );
    }

    return (
      <Form.Item
        key={key}
        name={key}
        label={key}
      >
        <Input {...commonProps} />
      </Form.Item>
    );
  };

  if (!visible || !node) {
    return null;
  }

  return (
    <Card
      title={
        <Space>
          <SettingOutlined />
          <Title level={5} style={{ margin: 0 }}>
            节点属性
          </Title>
        </Space>
      }
      size="small"
      style={{ height: '100%' }}
      bodyStyle={{ padding: '12px', height: 'calc(100% - 57px)', overflow: 'auto' }}
      extra={
        <Space>
          <Tooltip title="重置">
            <Button
              type="text"
              size="small"
              icon={<ReloadOutlined />}
              onClick={handleReset}
            />
          </Tooltip>
          <Tooltip title="保存">
            <Button
              type="text"
              size="small"
              icon={<SaveOutlined />}
              loading={loading}
              onClick={handleSave}
            />
          </Tooltip>
        </Space>
      }
    >
      {/* 节点基本信息 */}
      <Alert
        message={
          <Space direction="vertical" size="small" style={{ width: '100%' }}>
            <Space>
              <Text strong>{node.name}</Text>
              <Tag color={node.color}>{node.type}</Tag>
            </Space>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {node.description}
            </Text>
          </Space>
        }
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />

      {/* 节点标签 */}
      <div style={{ marginBottom: 16 }}>
        <Text strong style={{ fontSize: '12px' }}>标签：</Text>
        <div style={{ marginTop: 4 }}>
          {node.tags.map((tag, index) => (
            <Tag key={index} size="small" style={{ marginBottom: 4 }}>
              {tag}
            </Tag>
          ))}
        </div>
      </div>

      <Divider style={{ margin: '12px 0' }} />
      
      <Form
        form={form}
        layout="vertical"
        size="small"
      >
        <Collapse size="small" defaultActiveKey={['basic', 'advanced']}>
          <Panel header="基础属性" key="basic">
            {Object.entries(properties)
              .filter(([key]) => ['enabled', 'timeout', 'retryCount', 'logLevel'].includes(key))
              .map(([key, value]) => renderPropertyField(key, value))
            }
          </Panel>
          
          <Panel header="高级属性" key="advanced">
            {Object.entries(properties)
              .filter(([key]) => !['enabled', 'timeout', 'retryCount', 'logLevel'].includes(key))
              .map(([key, value]) => renderPropertyField(key, value))
            }
          </Panel>
        </Collapse>
      </Form>

      <Divider style={{ margin: '12px 0' }} />

      {/* 操作按钮 */}
      <Space style={{ width: '100%', justifyContent: 'center' }}>
        <Button
          type="default"
          size="small"
          icon={<ReloadOutlined />}
          onClick={handleReset}
        >
          重置
        </Button>
        <Button
          type="primary"
          size="small"
          icon={<SaveOutlined />}
          loading={loading}
          onClick={handleSave}
        >
          保存
        </Button>
      </Space>
    </Card>
  );
};

export default ServerNodePropertyEditor;
