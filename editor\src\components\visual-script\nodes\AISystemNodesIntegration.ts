/**
 * AI系统节点集成
 * 集成批次3：AI系统面板（82个节点）的集成实现
 */

import { NodeRegistry } from '../../../../engine/src/visual-script/registry/NodeRegistry';
import { aiCoreSystemNodesRegistry } from '../../../../engine/src/visual-script/registry/AICoreSystemNodesRegistry';
import { aiExtensionNodesRegistry } from '../../../../engine/src/visual-script/registry/AIExtensionNodesRegistry';

// AI系统节点类型定义
export interface AISystemNodeType {
  id: string;
  name: string;
  type: string;
  category: string;
  description: string;
  complexity: 'basic' | 'intermediate' | 'advanced';
  tags: string[];
  inputs: Array<{
    name: string;
    type: string;
    description: string;
    required?: boolean;
  }>;
  outputs: Array<{
    name: string;
    type: string;
    description: string;
  }>;
  properties: Array<{
    name: string;
    type: string;
    defaultValue: any;
    description: string;
  }>;
}

// AI系统节点分类
export enum AISystemNodeCategory {
  DEEP_LEARNING = 'deepLearning',
  MACHINE_LEARNING = 'machineLearning',
  AI_TOOLS = 'aiTools',
  AI_SERVICES = 'aiServices',
  NATURAL_LANGUAGE_PROCESSING = 'naturalLanguageProcessing',
  MODEL_MANAGEMENT = 'modelManagement'
}

/**
 * AI系统节点集成类
 */
export class AISystemNodesIntegration {
  private static instance: AISystemNodesIntegration;
  private isInitialized = false;
  private registeredNodes: Map<string, AISystemNodeType> = new Map();

  private constructor() {}

  public static getInstance(): AISystemNodesIntegration {
    if (!AISystemNodesIntegration.instance) {
      AISystemNodesIntegration.instance = new AISystemNodesIntegration();
    }
    return AISystemNodesIntegration.instance;
  }

  /**
   * 初始化AI系统节点集成
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.log('AI系统节点集成已初始化');
      return;
    }

    try {
      console.log('开始初始化AI系统节点集成...');

      // 注册AI核心系统节点
      await this.registerAICoreSystemNodes();

      // 注册AI扩展节点
      await this.registerAIExtensionNodes();

      // 验证节点注册
      await this.validateNodeRegistration();

      this.isInitialized = true;
      console.log('AI系统节点集成初始化完成');

    } catch (error) {
      console.error('AI系统节点集成初始化失败:', error);
      throw error;
    }
  }

  /**
   * 注册AI核心系统节点
   */
  private async registerAICoreSystemNodes(): Promise<void> {
    try {
      // 注册AI核心系统节点（21个）
      aiCoreSystemNodesRegistry.registerAllNodes();
      
      const coreNodeCount = aiCoreSystemNodesRegistry.getRegisteredNodeCount();
      console.log(`AI核心系统节点注册完成: ${coreNodeCount}个`);

      // 获取节点统计信息
      const stats = aiCoreSystemNodesRegistry.getNodeCategoryStats();
      console.log('AI核心系统节点统计:', stats);

    } catch (error) {
      console.error('AI核心系统节点注册失败:', error);
      throw error;
    }
  }

  /**
   * 注册AI扩展节点
   */
  private async registerAIExtensionNodes(): Promise<void> {
    try {
      // 注册AI扩展节点（61个）
      aiExtensionNodesRegistry.registerAllNodes();
      
      const extensionNodeCount = aiExtensionNodesRegistry.getRegisteredNodeCount();
      console.log(`AI扩展节点注册完成: ${extensionNodeCount}个`);

      // 获取节点统计信息
      const stats = aiExtensionNodesRegistry.getNodeCategoryStats();
      console.log('AI扩展节点统计:', stats);

    } catch (error) {
      console.error('AI扩展节点注册失败:', error);
      throw error;
    }
  }

  /**
   * 验证节点注册
   */
  private async validateNodeRegistration(): Promise<void> {
    try {
      // 验证深度学习节点
      const deepLearningNodes = this.getNodesByCategory(AISystemNodeCategory.DEEP_LEARNING);
      console.log(`深度学习节点验证: ${deepLearningNodes.length}个`);

      // 验证机器学习节点
      const machineLearningNodes = this.getNodesByCategory(AISystemNodeCategory.MACHINE_LEARNING);
      console.log(`机器学习节点验证: ${machineLearningNodes.length}个`);

      // 验证AI工具节点
      const aiToolsNodes = this.getNodesByCategory(AISystemNodeCategory.AI_TOOLS);
      console.log(`AI工具节点验证: ${aiToolsNodes.length}个`);

      // 验证AI服务节点
      const aiServicesNodes = this.getNodesByCategory(AISystemNodeCategory.AI_SERVICES);
      console.log(`AI服务节点验证: ${aiServicesNodes.length}个`);

      // 验证自然语言处理节点
      const nlpNodes = this.getNodesByCategory(AISystemNodeCategory.NATURAL_LANGUAGE_PROCESSING);
      console.log(`自然语言处理节点验证: ${nlpNodes.length}个`);

      // 验证模型管理节点
      const modelManagementNodes = this.getNodesByCategory(AISystemNodeCategory.MODEL_MANAGEMENT);
      console.log(`模型管理节点验证: ${modelManagementNodes.length}个`);

      const totalNodes = deepLearningNodes.length + machineLearningNodes.length + 
                        aiToolsNodes.length + aiServicesNodes.length + 
                        nlpNodes.length + modelManagementNodes.length;
      
      console.log(`AI系统节点总计: ${totalNodes}个`);

      if (totalNodes < 82) {
        console.warn(`警告: AI系统节点数量不足，期望82个，实际${totalNodes}个`);
      }

    } catch (error) {
      console.error('节点注册验证失败:', error);
      throw error;
    }
  }

  /**
   * 根据分类获取节点
   */
  public getNodesByCategory(category: AISystemNodeCategory): AISystemNodeType[] {
    const nodes: AISystemNodeType[] = [];
    
    // 从NodeRegistry获取节点
    try {
      const categoryNodes = NodeRegistry.getNodesByCategory(category);
      return categoryNodes.map(node => this.convertToAISystemNodeType(node));
    } catch (error) {
      console.error(`获取${category}分类节点失败:`, error);
      return nodes;
    }
  }

  /**
   * 转换为AI系统节点类型
   */
  private convertToAISystemNodeType(node: any): AISystemNodeType {
    return {
      id: node.id || node.type,
      name: node.name || node.type,
      type: node.type,
      category: node.category || 'unknown',
      description: node.description || '',
      complexity: node.complexity || 'basic',
      tags: node.tags || [],
      inputs: node.inputs || [],
      outputs: node.outputs || [],
      properties: node.properties || []
    };
  }

  /**
   * 获取所有AI系统节点
   */
  public getAllNodes(): AISystemNodeType[] {
    const allNodes: AISystemNodeType[] = [];
    
    // 获取所有分类的节点
    Object.values(AISystemNodeCategory).forEach(category => {
      const categoryNodes = this.getNodesByCategory(category);
      allNodes.push(...categoryNodes);
    });

    return allNodes;
  }

  /**
   * 根据类型获取节点
   */
  public getNodeByType(nodeType: string): AISystemNodeType | undefined {
    try {
      const node = NodeRegistry.getNode(nodeType);
      return node ? this.convertToAISystemNodeType(node) : undefined;
    } catch (error) {
      console.error(`获取节点${nodeType}失败:`, error);
      return undefined;
    }
  }

  /**
   * 创建节点实例
   */
  public async createNodeInstance(nodeType: string): Promise<any> {
    try {
      const nodeInstance = NodeRegistry.createNode(nodeType);
      console.log(`创建AI节点实例: ${nodeType}`);
      return nodeInstance;
    } catch (error) {
      console.error(`创建AI节点实例失败: ${nodeType}`, error);
      throw error;
    }
  }

  /**
   * 获取集成状态
   */
  public getIntegrationStatus(): {
    isInitialized: boolean;
    totalNodes: number;
    nodesByCategory: Record<string, number>;
  } {
    const nodesByCategory: Record<string, number> = {};
    
    Object.values(AISystemNodeCategory).forEach(category => {
      nodesByCategory[category] = this.getNodesByCategory(category).length;
    });

    return {
      isInitialized: this.isInitialized,
      totalNodes: this.getAllNodes().length,
      nodesByCategory
    };
  }

  /**
   * 重置集成状态
   */
  public reset(): void {
    this.isInitialized = false;
    this.registeredNodes.clear();
    console.log('AI系统节点集成已重置');
  }
}

// 导出单例实例
export const aiSystemNodesIntegration = AISystemNodesIntegration.getInstance();
