/**
 * 服务器系统节点面板组件
 * 集成批次4：显示和管理58个服务器系统节点，包括7个子分类
 */

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  Card,
  Collapse,
  List,
  Tag,
  Space,
  Button,
  Tooltip,
  Badge,
  Typography,
  Divider,
  Empty,
  Spin,
  Input,
  Select,
  Row,
  Col
} from 'antd';
import {
  ServerOutlined,
  DatabaseOutlined,
  FileOutlined,
  SafetyOutlined,
  BellOutlined,
  MonitorOutlined,
  ProjectOutlined,
  InfoCircleOutlined,
  PlusOutlined,
  SearchOutlined,
  SettingOutlined,
  EyeOutlined,
  BuildOutlined,
  UserOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { DraggableServerNode } from '../nodes/ServerNodeDragDrop';

const { Panel } = Collapse;
const { Text, Title } = Typography;
const { Search } = Input;
const { Option } = Select;

// 服务器节点分类枚举
export enum ServerNodeCategory {
  USER_SERVICE = 'userService',
  DATA_SERVICE = 'dataService', 
  FILE_SERVICE = 'fileService',
  AUTH_SERVICE = 'authService',
  NOTIFICATION_SERVICE = 'notificationService',
  MONITORING_SERVICE = 'monitoringService',
  PROJECT_MANAGEMENT = 'projectManagement'
}

// 服务器节点项接口
export interface ServerNodeItem {
  id: string;
  type: string;
  name: string;
  description: string;
  category: ServerNodeCategory;
  icon: string;
  color: string;
  complexity: 'basic' | 'intermediate' | 'advanced';
  tags: string[];
  inputs: Array<{
    name: string;
    type: string;
    description: string;
    required?: boolean;
  }>;
  outputs: Array<{
    name: string;
    type: string;
    description: string;
  }>;
  properties: Array<{
    name: string;
    type: string;
    defaultValue: any;
    description: string;
  }>;
  version: string;
  author: string;
  documentation?: string;
}

// 组件属性接口
interface ServerSystemNodesPanelProps {
  visible?: boolean;
  onNodeSelect?: (node: ServerNodeItem) => void;
  onNodeAdd?: (nodeType: string) => void;
  height?: number | string;
  width?: number | string;
}

/**
 * 服务器系统节点面板组件
 */
const ServerSystemNodesPanel: React.FC<ServerSystemNodesPanelProps> = ({
  visible = true,
  onNodeSelect,
  onNodeAdd,
  height = '100%',
  width = '100%'
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [expandedPanels, setExpandedPanels] = useState<string[]>(['userService']);
  const [filteredNodes, setFilteredNodes] = useState<ServerNodeItem[]>([]);

  // 服务器系统节点数据
  const serverNodes: ServerNodeItem[] = useMemo(() => [
    // 用户服务节点（12个）
    {
      id: 'user-registration-node',
      type: 'UserRegistrationNode',
      name: '用户注册',
      description: '处理用户注册请求，验证用户信息并创建账户',
      category: ServerNodeCategory.USER_SERVICE,
      icon: '👤',
      color: '#1890ff',
      complexity: 'basic',
      tags: ['用户', '注册', '认证'],
      inputs: [
        { name: 'userInfo', type: 'object', description: '用户注册信息', required: true },
        { name: 'validationRules', type: 'object', description: '验证规则配置' }
      ],
      outputs: [
        { name: 'userId', type: 'string', description: '新创建的用户ID' },
        { name: 'success', type: 'boolean', description: '注册是否成功' }
      ],
      properties: [
        { name: 'enableEmailVerification', type: 'boolean', defaultValue: true, description: '启用邮箱验证' },
        { name: 'passwordMinLength', type: 'number', defaultValue: 8, description: '密码最小长度' }
      ],
      version: '1.0.0',
      author: 'DL Engine Team'
    },
    {
      id: 'user-authentication-node',
      type: 'UserAuthenticationNode', 
      name: '用户认证',
      description: '验证用户身份，支持多种认证方式',
      category: ServerNodeCategory.USER_SERVICE,
      icon: '🔐',
      color: '#1890ff',
      complexity: 'intermediate',
      tags: ['用户', '认证', '登录'],
      inputs: [
        { name: 'credentials', type: 'object', description: '用户凭证', required: true },
        { name: 'authMethod', type: 'string', description: '认证方式' }
      ],
      outputs: [
        { name: 'authToken', type: 'string', description: '认证令牌' },
        { name: 'userInfo', type: 'object', description: '用户信息' }
      ],
      properties: [
        { name: 'tokenExpiry', type: 'number', defaultValue: 3600, description: '令牌过期时间（秒）' },
        { name: 'maxAttempts', type: 'number', defaultValue: 3, description: '最大尝试次数' }
      ],
      version: '1.0.0',
      author: 'DL Engine Team'
    },
    {
      id: 'user-profile-node',
      type: 'UserProfileNode',
      name: '用户资料',
      description: '管理用户个人资料信息',
      category: ServerNodeCategory.USER_SERVICE,
      icon: '📋',
      color: '#1890ff',
      complexity: 'basic',
      tags: ['用户', '资料', '管理'],
      inputs: [
        { name: 'userId', type: 'string', description: '用户ID', required: true },
        { name: 'profileData', type: 'object', description: '资料数据' }
      ],
      outputs: [
        { name: 'profile', type: 'object', description: '用户资料' },
        { name: 'updated', type: 'boolean', description: '是否更新成功' }
      ],
      properties: [
        { name: 'allowPublicProfile', type: 'boolean', defaultValue: false, description: '允许公开资料' },
        { name: 'requiredFields', type: 'array', defaultValue: ['name', 'email'], description: '必填字段' }
      ],
      version: '1.0.0',
      author: 'DL Engine Team'
    },
    {
      id: 'user-permission-node',
      type: 'UserPermissionNode',
      name: '用户权限',
      description: '管理用户权限和访问控制',
      category: ServerNodeCategory.USER_SERVICE,
      icon: '🔑',
      color: '#1890ff',
      complexity: 'advanced',
      tags: ['用户', '权限', '访问控制'],
      inputs: [
        { name: 'userId', type: 'string', description: '用户ID', required: true },
        { name: 'resource', type: 'string', description: '资源标识' },
        { name: 'action', type: 'string', description: '操作类型' }
      ],
      outputs: [
        { name: 'hasPermission', type: 'boolean', description: '是否有权限' },
        { name: 'permissions', type: 'array', description: '权限列表' }
      ],
      properties: [
        { name: 'inheritFromGroup', type: 'boolean', defaultValue: true, description: '继承组权限' },
        { name: 'cachePermissions', type: 'boolean', defaultValue: true, description: '缓存权限' }
      ],
      version: '1.0.0',
      author: 'DL Engine Team'
    },
    {
      id: 'user-session-node',
      type: 'UserSessionNode',
      name: '用户会话',
      description: '管理用户会话状态和生命周期',
      category: ServerNodeCategory.USER_SERVICE,
      icon: '⏱️',
      color: '#1890ff',
      complexity: 'intermediate',
      tags: ['用户', '会话', '状态'],
      inputs: [
        { name: 'sessionId', type: 'string', description: '会话ID', required: true },
        { name: 'action', type: 'string', description: '会话操作' }
      ],
      outputs: [
        { name: 'sessionData', type: 'object', description: '会话数据' },
        { name: 'isValid', type: 'boolean', description: '会话是否有效' }
      ],
      properties: [
        { name: 'sessionTimeout', type: 'number', defaultValue: 1800, description: '会话超时时间（秒）' },
        { name: 'renewOnActivity', type: 'boolean', defaultValue: true, description: '活动时续期' }
      ],
      version: '1.0.0',
      author: 'DL Engine Team'
    },
    {
      id: 'user-preferences-node',
      type: 'UserPreferencesNode',
      name: '用户偏好',
      description: '管理用户偏好设置和个性化配置',
      category: ServerNodeCategory.USER_SERVICE,
      icon: '⚙️',
      color: '#1890ff',
      complexity: 'basic',
      tags: ['用户', '偏好', '设置'],
      inputs: [
        { name: 'userId', type: 'string', description: '用户ID', required: true },
        { name: 'preferences', type: 'object', description: '偏好设置' }
      ],
      outputs: [
        { name: 'userPreferences', type: 'object', description: '用户偏好' },
        { name: 'saved', type: 'boolean', description: '是否保存成功' }
      ],
      properties: [
        { name: 'defaultTheme', type: 'string', defaultValue: 'light', description: '默认主题' },
        { name: 'autoSave', type: 'boolean', defaultValue: true, description: '自动保存' }
      ],
      version: '1.0.0',
      author: 'DL Engine Team'
    },
    {
      id: 'user-activity-node',
      type: 'UserActivityNode',
      name: '用户活动',
      description: '记录和分析用户活动日志',
      category: ServerNodeCategory.USER_SERVICE,
      icon: '📊',
      color: '#1890ff',
      complexity: 'intermediate',
      tags: ['用户', '活动', '日志'],
      inputs: [
        { name: 'userId', type: 'string', description: '用户ID', required: true },
        { name: 'activity', type: 'object', description: '活动数据' }
      ],
      outputs: [
        { name: 'activityLog', type: 'array', description: '活动日志' },
        { name: 'statistics', type: 'object', description: '活动统计' }
      ],
      properties: [
        { name: 'retentionDays', type: 'number', defaultValue: 90, description: '日志保留天数' },
        { name: 'enableAnalytics', type: 'boolean', defaultValue: true, description: '启用分析' }
      ],
      version: '1.0.0',
      author: 'DL Engine Team'
    },
    {
      id: 'user-notification-node',
      type: 'UserNotificationNode',
      name: '用户通知',
      description: '发送用户通知和消息',
      category: ServerNodeCategory.USER_SERVICE,
      icon: '📢',
      color: '#1890ff',
      complexity: 'basic',
      tags: ['用户', '通知', '消息'],
      inputs: [
        { name: 'userId', type: 'string', description: '用户ID', required: true },
        { name: 'message', type: 'object', description: '通知消息', required: true }
      ],
      outputs: [
        { name: 'sent', type: 'boolean', description: '是否发送成功' },
        { name: 'deliveryId', type: 'string', description: '投递ID' }
      ],
      properties: [
        { name: 'channels', type: 'array', defaultValue: ['email', 'push'], description: '通知渠道' },
        { name: 'priority', type: 'string', defaultValue: 'normal', description: '优先级' }
      ],
      version: '1.0.0',
      author: 'DL Engine Team'
    },
    {
      id: 'user-group-node',
      type: 'UserGroupNode',
      name: '用户组',
      description: '管理用户组和组成员关系',
      category: ServerNodeCategory.USER_SERVICE,
      icon: '👥',
      color: '#1890ff',
      complexity: 'intermediate',
      tags: ['用户', '组', '管理'],
      inputs: [
        { name: 'groupId', type: 'string', description: '组ID' },
        { name: 'userId', type: 'string', description: '用户ID' },
        { name: 'action', type: 'string', description: '操作类型' }
      ],
      outputs: [
        { name: 'groupInfo', type: 'object', description: '组信息' },
        { name: 'members', type: 'array', description: '组成员' }
      ],
      properties: [
        { name: 'maxMembers', type: 'number', defaultValue: 100, description: '最大成员数' },
        { name: 'allowSelfJoin', type: 'boolean', defaultValue: false, description: '允许自主加入' }
      ],
      version: '1.0.0',
      author: 'DL Engine Team'
    },
    {
      id: 'user-role-node',
      type: 'UserRoleNode',
      name: '用户角色',
      description: '管理用户角色和角色权限',
      category: ServerNodeCategory.USER_SERVICE,
      icon: '🎭',
      color: '#1890ff',
      complexity: 'advanced',
      tags: ['用户', '角色', '权限'],
      inputs: [
        { name: 'userId', type: 'string', description: '用户ID', required: true },
        { name: 'roleId', type: 'string', description: '角色ID' }
      ],
      outputs: [
        { name: 'userRoles', type: 'array', description: '用户角色' },
        { name: 'rolePermissions', type: 'array', description: '角色权限' }
      ],
      properties: [
        { name: 'allowMultipleRoles', type: 'boolean', defaultValue: true, description: '允许多角色' },
        { name: 'inheritPermissions', type: 'boolean', defaultValue: true, description: '继承权限' }
      ],
      version: '1.0.0',
      author: 'DL Engine Team'
    },
    {
      id: 'user-security-node',
      type: 'UserSecurityNode',
      name: '用户安全',
      description: '管理用户安全设置和安全策略',
      category: ServerNodeCategory.USER_SERVICE,
      icon: '🛡️',
      color: '#1890ff',
      complexity: 'advanced',
      tags: ['用户', '安全', '策略'],
      inputs: [
        { name: 'userId', type: 'string', description: '用户ID', required: true },
        { name: 'securityEvent', type: 'object', description: '安全事件' }
      ],
      outputs: [
        { name: 'securityStatus', type: 'object', description: '安全状态' },
        { name: 'riskLevel', type: 'string', description: '风险等级' }
      ],
      properties: [
        { name: 'enableTwoFactor', type: 'boolean', defaultValue: false, description: '启用双因子认证' },
        { name: 'passwordPolicy', type: 'object', defaultValue: {}, description: '密码策略' }
      ],
      version: '1.0.0',
      author: 'DL Engine Team'
    },
    {
      id: 'user-analytics-node',
      type: 'UserAnalyticsNode',
      name: '用户分析',
      description: '分析用户行为数据和使用模式',
      category: ServerNodeCategory.USER_SERVICE,
      icon: '📈',
      color: '#1890ff',
      complexity: 'advanced',
      tags: ['用户', '分析', '数据'],
      inputs: [
        { name: 'userId', type: 'string', description: '用户ID' },
        { name: 'timeRange', type: 'object', description: '时间范围' }
      ],
      outputs: [
        { name: 'analytics', type: 'object', description: '分析结果' },
        { name: 'insights', type: 'array', description: '洞察信息' }
      ],
      properties: [
        { name: 'aggregationLevel', type: 'string', defaultValue: 'daily', description: '聚合级别' },
        { name: 'includePersonalData', type: 'boolean', defaultValue: false, description: '包含个人数据' }
      ],
      version: '1.0.0',
      author: 'DL Engine Team'
    },

    // 数据服务节点（12个）
    {
      id: 'database-connection-node',
      type: 'DatabaseConnectionNode',
      name: '数据库连接',
      description: '管理数据库连接和连接池',
      category: ServerNodeCategory.DATA_SERVICE,
      icon: '🔗',
      color: '#52c41a',
      complexity: 'intermediate',
      tags: ['数据库', '连接', '管理'],
      inputs: [
        { name: 'connectionConfig', type: 'object', description: '连接配置', required: true },
        { name: 'poolSize', type: 'number', description: '连接池大小' }
      ],
      outputs: [
        { name: 'connection', type: 'object', description: '数据库连接' },
        { name: 'status', type: 'string', description: '连接状态' }
      ],
      properties: [
        { name: 'maxConnections', type: 'number', defaultValue: 10, description: '最大连接数' },
        { name: 'timeout', type: 'number', defaultValue: 30000, description: '连接超时时间' }
      ],
      version: '1.0.0',
      author: 'DL Engine Team'
    },
    {
      id: 'database-query-node',
      type: 'DatabaseQueryNode',
      name: '数据库查询',
      description: '执行数据库查询操作',
      category: ServerNodeCategory.DATA_SERVICE,
      icon: '🔍',
      color: '#52c41a',
      complexity: 'basic',
      tags: ['数据库', '查询', 'SQL'],
      inputs: [
        { name: 'query', type: 'string', description: 'SQL查询语句', required: true },
        { name: 'parameters', type: 'array', description: '查询参数' }
      ],
      outputs: [
        { name: 'result', type: 'array', description: '查询结果' },
        { name: 'rowCount', type: 'number', description: '结果行数' }
      ],
      properties: [
        { name: 'enableCache', type: 'boolean', defaultValue: true, description: '启用缓存' },
        { name: 'queryTimeout', type: 'number', defaultValue: 30, description: '查询超时时间' }
      ],
      version: '1.0.0',
      author: 'DL Engine Team'
    },
    {
      id: 'data-validation-node',
      type: 'DataValidationNode',
      name: '数据验证',
      description: '验证数据格式和完整性',
      category: ServerNodeCategory.DATA_SERVICE,
      icon: '✅',
      color: '#52c41a',
      complexity: 'intermediate',
      tags: ['数据', '验证', '校验'],
      inputs: [
        { name: 'data', type: 'any', description: '待验证数据', required: true },
        { name: 'schema', type: 'object', description: '验证模式' }
      ],
      outputs: [
        { name: 'isValid', type: 'boolean', description: '是否有效' },
        { name: 'errors', type: 'array', description: '验证错误' }
      ],
      properties: [
        { name: 'strictMode', type: 'boolean', defaultValue: false, description: '严格模式' },
        { name: 'allowUnknown', type: 'boolean', defaultValue: true, description: '允许未知字段' }
      ],
      version: '1.0.0',
      author: 'DL Engine Team'
    },
    {
      id: 'data-transformation-node',
      type: 'DataTransformationNode',
      name: '数据转换',
      description: '转换数据格式和结构',
      category: ServerNodeCategory.DATA_SERVICE,
      icon: '🔄',
      color: '#52c41a',
      complexity: 'intermediate',
      tags: ['数据', '转换', '格式'],
      inputs: [
        { name: 'inputData', type: 'any', description: '输入数据', required: true },
        { name: 'transformRules', type: 'object', description: '转换规则' }
      ],
      outputs: [
        { name: 'outputData', type: 'any', description: '输出数据' },
        { name: 'transformedCount', type: 'number', description: '转换数量' }
      ],
      properties: [
        { name: 'preserveOriginal', type: 'boolean', defaultValue: false, description: '保留原始数据' },
        { name: 'batchSize', type: 'number', defaultValue: 1000, description: '批处理大小' }
      ],
      version: '1.0.0',
      author: 'DL Engine Team'
    },
    {
      id: 'data-cache-node',
      type: 'DataCacheNode',
      name: '数据缓存',
      description: '缓存常用数据以提高性能',
      category: ServerNodeCategory.DATA_SERVICE,
      icon: '💾',
      color: '#52c41a',
      complexity: 'intermediate',
      tags: ['数据', '缓存', '性能'],
      inputs: [
        { name: 'key', type: 'string', description: '缓存键', required: true },
        { name: 'value', type: 'any', description: '缓存值' },
        { name: 'ttl', type: 'number', description: '生存时间' }
      ],
      outputs: [
        { name: 'cachedValue', type: 'any', description: '缓存值' },
        { name: 'hit', type: 'boolean', description: '是否命中' }
      ],
      properties: [
        { name: 'defaultTTL', type: 'number', defaultValue: 3600, description: '默认生存时间' },
        { name: 'maxSize', type: 'number', defaultValue: 1000, description: '最大缓存数量' }
      ],
      version: '1.0.0',
      author: 'DL Engine Team'
    },
    {
      id: 'data-backup-node',
      type: 'DataBackupNode',
      name: '数据备份',
      description: '备份重要数据到安全存储',
      category: ServerNodeCategory.DATA_SERVICE,
      icon: '💿',
      color: '#52c41a',
      complexity: 'advanced',
      tags: ['数据', '备份', '安全'],
      inputs: [
        { name: 'dataSource', type: 'string', description: '数据源', required: true },
        { name: 'backupConfig', type: 'object', description: '备份配置' }
      ],
      outputs: [
        { name: 'backupId', type: 'string', description: '备份ID' },
        { name: 'success', type: 'boolean', description: '备份是否成功' }
      ],
      properties: [
        { name: 'compression', type: 'boolean', defaultValue: true, description: '启用压缩' },
        { name: 'encryption', type: 'boolean', defaultValue: true, description: '启用加密' }
      ],
      version: '1.0.0',
      author: 'DL Engine Team'
    },

    // 文件服务节点（10个）
    {
      id: 'file-upload-node',
      type: 'FileUploadNode',
      name: '文件上传',
      description: '处理文件上传和存储',
      category: ServerNodeCategory.FILE_SERVICE,
      icon: '📤',
      color: '#fa8c16',
      complexity: 'basic',
      tags: ['文件', '上传', '存储'],
      inputs: [
        { name: 'file', type: 'file', description: '上传文件', required: true },
        { name: 'uploadConfig', type: 'object', description: '上传配置' }
      ],
      outputs: [
        { name: 'fileId', type: 'string', description: '文件ID' },
        { name: 'fileUrl', type: 'string', description: '文件URL' }
      ],
      properties: [
        { name: 'maxFileSize', type: 'number', defaultValue: 10485760, description: '最大文件大小（字节）' },
        { name: 'allowedTypes', type: 'array', defaultValue: ['*'], description: '允许的文件类型' }
      ],
      version: '1.0.0',
      author: 'DL Engine Team'
    },
    {
      id: 'file-download-node',
      type: 'FileDownloadNode',
      name: '文件下载',
      description: '提供文件下载服务',
      category: ServerNodeCategory.FILE_SERVICE,
      icon: '📥',
      color: '#fa8c16',
      complexity: 'basic',
      tags: ['文件', '下载', '服务'],
      inputs: [
        { name: 'fileId', type: 'string', description: '文件ID', required: true },
        { name: 'downloadOptions', type: 'object', description: '下载选项' }
      ],
      outputs: [
        { name: 'fileStream', type: 'stream', description: '文件流' },
        { name: 'metadata', type: 'object', description: '文件元数据' }
      ],
      properties: [
        { name: 'enableResume', type: 'boolean', defaultValue: true, description: '支持断点续传' },
        { name: 'compressionLevel', type: 'number', defaultValue: 6, description: '压缩级别' }
      ],
      version: '1.0.0',
      author: 'DL Engine Team'
    },

    // 认证授权节点（7个）
    {
      id: 'jwt-token-node',
      type: 'JWTTokenNode',
      name: 'JWT令牌',
      description: '生成和验证JWT令牌',
      category: ServerNodeCategory.AUTH_SERVICE,
      icon: '🎫',
      color: '#722ed1',
      complexity: 'intermediate',
      tags: ['JWT', '令牌', '认证'],
      inputs: [
        { name: 'payload', type: 'object', description: '令牌载荷', required: true },
        { name: 'secret', type: 'string', description: '签名密钥' }
      ],
      outputs: [
        { name: 'token', type: 'string', description: 'JWT令牌' },
        { name: 'expiresAt', type: 'date', description: '过期时间' }
      ],
      properties: [
        { name: 'algorithm', type: 'string', defaultValue: 'HS256', description: '签名算法' },
        { name: 'expiresIn', type: 'string', defaultValue: '1h', description: '过期时间' }
      ],
      version: '1.0.0',
      author: 'DL Engine Team'
    },
    {
      id: 'oauth2-node',
      type: 'OAuth2Node',
      name: 'OAuth2认证',
      description: '实现OAuth2认证流程',
      category: ServerNodeCategory.AUTH_SERVICE,
      icon: '🔐',
      color: '#722ed1',
      complexity: 'advanced',
      tags: ['OAuth2', '认证', '授权'],
      inputs: [
        { name: 'clientId', type: 'string', description: '客户端ID', required: true },
        { name: 'redirectUri', type: 'string', description: '重定向URI' }
      ],
      outputs: [
        { name: 'authCode', type: 'string', description: '授权码' },
        { name: 'accessToken', type: 'string', description: '访问令牌' }
      ],
      properties: [
        { name: 'grantType', type: 'string', defaultValue: 'authorization_code', description: '授权类型' },
        { name: 'scope', type: 'array', defaultValue: ['read'], description: '权限范围' }
      ],
      version: '1.0.0',
      author: 'DL Engine Team'
    },

    // 通知服务节点（8个）
    {
      id: 'notification-service-node',
      type: 'NotificationServiceNode',
      name: '通知服务',
      description: '统一通知服务管理',
      category: ServerNodeCategory.NOTIFICATION_SERVICE,
      icon: '📢',
      color: '#eb2f96',
      complexity: 'intermediate',
      tags: ['通知', '服务', '管理'],
      inputs: [
        { name: 'message', type: 'object', description: '通知消息', required: true },
        { name: 'recipients', type: 'array', description: '接收者列表' }
      ],
      outputs: [
        { name: 'notificationId', type: 'string', description: '通知ID' },
        { name: 'deliveryStatus', type: 'object', description: '投递状态' }
      ],
      properties: [
        { name: 'defaultChannel', type: 'string', defaultValue: 'email', description: '默认通道' },
        { name: 'retryAttempts', type: 'number', defaultValue: 3, description: '重试次数' }
      ],
      version: '1.0.0',
      author: 'DL Engine Team'
    },
    {
      id: 'email-notification-node',
      type: 'EmailNotificationNode',
      name: '邮件通知',
      description: '发送邮件通知',
      category: ServerNodeCategory.NOTIFICATION_SERVICE,
      icon: '📧',
      color: '#eb2f96',
      complexity: 'basic',
      tags: ['邮件', '通知', '发送'],
      inputs: [
        { name: 'to', type: 'string', description: '收件人', required: true },
        { name: 'subject', type: 'string', description: '邮件主题', required: true },
        { name: 'content', type: 'string', description: '邮件内容', required: true }
      ],
      outputs: [
        { name: 'messageId', type: 'string', description: '消息ID' },
        { name: 'sent', type: 'boolean', description: '是否发送成功' }
      ],
      properties: [
        { name: 'smtpServer', type: 'string', defaultValue: 'smtp.gmail.com', description: 'SMTP服务器' },
        { name: 'port', type: 'number', defaultValue: 587, description: '端口号' }
      ],
      version: '1.0.0',
      author: 'DL Engine Team'
    },
    {
      id: 'push-notification-node',
      type: 'PushNotificationNode',
      name: '推送通知',
      description: '发送移动端推送通知',
      category: ServerNodeCategory.NOTIFICATION_SERVICE,
      icon: '📱',
      color: '#eb2f96',
      complexity: 'intermediate',
      tags: ['推送', '移动端', '通知'],
      inputs: [
        { name: 'deviceToken', type: 'string', description: '设备令牌', required: true },
        { name: 'title', type: 'string', description: '通知标题', required: true },
        { name: 'body', type: 'string', description: '通知内容', required: true }
      ],
      outputs: [
        { name: 'pushId', type: 'string', description: '推送ID' },
        { name: 'delivered', type: 'boolean', description: '是否投递成功' }
      ],
      properties: [
        { name: 'platform', type: 'string', defaultValue: 'both', description: '推送平台' },
        { name: 'priority', type: 'string', defaultValue: 'normal', description: '推送优先级' }
      ],
      version: '1.0.0',
      author: 'DL Engine Team'
    },

    // 监控服务节点（5个）
    {
      id: 'system-monitoring-node',
      type: 'SystemMonitoringNode',
      name: '系统监控',
      description: '监控系统资源使用情况',
      category: ServerNodeCategory.MONITORING_SERVICE,
      icon: '📊',
      color: '#13c2c2',
      complexity: 'intermediate',
      tags: ['系统', '监控', '资源'],
      inputs: [
        { name: 'monitorConfig', type: 'object', description: '监控配置' },
        { name: 'interval', type: 'number', description: '监控间隔' }
      ],
      outputs: [
        { name: 'metrics', type: 'object', description: '系统指标' },
        { name: 'alerts', type: 'array', description: '告警信息' }
      ],
      properties: [
        { name: 'cpuThreshold', type: 'number', defaultValue: 80, description: 'CPU告警阈值' },
        { name: 'memoryThreshold', type: 'number', defaultValue: 85, description: '内存告警阈值' }
      ],
      version: '1.0.0',
      author: 'DL Engine Team'
    },
    {
      id: 'performance-analysis-node',
      type: 'PerformanceAnalysisNode',
      name: '性能分析',
      description: '分析系统性能指标',
      category: ServerNodeCategory.MONITORING_SERVICE,
      icon: '⚡',
      color: '#13c2c2',
      complexity: 'advanced',
      tags: ['性能', '分析', '指标'],
      inputs: [
        { name: 'performanceData', type: 'object', description: '性能数据', required: true },
        { name: 'analysisType', type: 'string', description: '分析类型' }
      ],
      outputs: [
        { name: 'analysisResult', type: 'object', description: '分析结果' },
        { name: 'recommendations', type: 'array', description: '优化建议' }
      ],
      properties: [
        { name: 'analysisDepth', type: 'string', defaultValue: 'standard', description: '分析深度' },
        { name: 'includeHistorical', type: 'boolean', defaultValue: true, description: '包含历史数据' }
      ],
      version: '1.0.0',
      author: 'DL Engine Team'
    },

    // 项目管理节点（4个）
    {
      id: 'project-management-node',
      type: 'ProjectManagementNode',
      name: '项目管理',
      description: '管理项目信息和状态',
      category: ServerNodeCategory.PROJECT_MANAGEMENT,
      icon: '📋',
      color: '#f759ab',
      complexity: 'intermediate',
      tags: ['项目', '管理', '状态'],
      inputs: [
        { name: 'projectId', type: 'string', description: '项目ID', required: true },
        { name: 'action', type: 'string', description: '操作类型' }
      ],
      outputs: [
        { name: 'projectInfo', type: 'object', description: '项目信息' },
        { name: 'status', type: 'string', description: '项目状态' }
      ],
      properties: [
        { name: 'autoSave', type: 'boolean', defaultValue: true, description: '自动保存' },
        { name: 'versionControl', type: 'boolean', defaultValue: true, description: '版本控制' }
      ],
      version: '1.0.0',
      author: 'DL Engine Team'
    },
    {
      id: 'task-management-node',
      type: 'TaskManagementNode',
      name: '任务管理',
      description: '管理项目任务和进度',
      category: ServerNodeCategory.PROJECT_MANAGEMENT,
      icon: '✅',
      color: '#f759ab',
      complexity: 'basic',
      tags: ['任务', '管理', '进度'],
      inputs: [
        { name: 'taskId', type: 'string', description: '任务ID' },
        { name: 'taskData', type: 'object', description: '任务数据' }
      ],
      outputs: [
        { name: 'task', type: 'object', description: '任务信息' },
        { name: 'progress', type: 'number', description: '完成进度' }
      ],
      properties: [
        { name: 'enableNotifications', type: 'boolean', defaultValue: true, description: '启用通知' },
        { name: 'trackTime', type: 'boolean', defaultValue: false, description: '时间跟踪' }
      ],
      version: '1.0.0',
      author: 'DL Engine Team'
    }
  ], []);

  // 过滤节点
  useEffect(() => {
    let filtered = serverNodes;

    // 按分类过滤
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(node => node.category === selectedCategory);
    }

    // 按搜索文本过滤
    if (searchText) {
      const searchLower = searchText.toLowerCase();
      filtered = filtered.filter(node =>
        node.name.toLowerCase().includes(searchLower) ||
        node.description.toLowerCase().includes(searchLower) ||
        node.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }

    setFilteredNodes(filtered);
  }, [serverNodes, selectedCategory, searchText]);

  // 处理搜索
  const handleSearch = useCallback((value: string) => {
    setSearchText(value);
  }, []);

  // 处理分类选择
  const handleCategoryChange = useCallback((value: string) => {
    setSelectedCategory(value);
  }, []);

  // 处理面板展开/收起
  const handlePanelChange = useCallback((keys: string | string[]) => {
    setExpandedPanels(Array.isArray(keys) ? keys : [keys]);
  }, []);

  // 获取分类图标
  const getCategoryIcon = (category: ServerNodeCategory) => {
    switch (category) {
      case ServerNodeCategory.USER_SERVICE:
        return <UserOutlined />;
      case ServerNodeCategory.DATA_SERVICE:
        return <DatabaseOutlined />;
      case ServerNodeCategory.FILE_SERVICE:
        return <FileOutlined />;
      case ServerNodeCategory.AUTH_SERVICE:
        return <SafetyOutlined />;
      case ServerNodeCategory.NOTIFICATION_SERVICE:
        return <BellOutlined />;
      case ServerNodeCategory.MONITORING_SERVICE:
        return <MonitorOutlined />;
      case ServerNodeCategory.PROJECT_MANAGEMENT:
        return <ProjectOutlined />;
      default:
        return <ServerOutlined />;
    }
  };

  // 获取分类名称
  const getCategoryName = (category: ServerNodeCategory) => {
    switch (category) {
      case ServerNodeCategory.USER_SERVICE:
        return '用户服务';
      case ServerNodeCategory.DATA_SERVICE:
        return '数据服务';
      case ServerNodeCategory.FILE_SERVICE:
        return '文件服务';
      case ServerNodeCategory.AUTH_SERVICE:
        return '认证授权';
      case ServerNodeCategory.NOTIFICATION_SERVICE:
        return '通知服务';
      case ServerNodeCategory.MONITORING_SERVICE:
        return '监控服务';
      case ServerNodeCategory.PROJECT_MANAGEMENT:
        return '项目管理';
      default:
        return '未知分类';
    }
  };

  // 按分类分组节点
  const nodesByCategory = useMemo(() => {
    const grouped = new Map<ServerNodeCategory, ServerNodeItem[]>();
    
    filteredNodes.forEach(node => {
      if (!grouped.has(node.category)) {
        grouped.set(node.category, []);
      }
      grouped.get(node.category)!.push(node);
    });

    return grouped;
  }, [filteredNodes]);

  if (!visible) {
    return null;
  }

  return (
    <Card
      title={
        <Space>
          <ServerOutlined />
          <Title level={4} style={{ margin: 0 }}>
            服务器系统节点
          </Title>
          <Badge count={58} style={{ backgroundColor: '#52c41a' }} />
        </Space>
      }
      size="small"
      style={{ height, width, overflow: 'hidden' }}
      bodyStyle={{ padding: '12px', height: 'calc(100% - 57px)', overflow: 'auto' }}
      extra={
        <Space>
          <Tooltip title="节点信息">
            <Button
              type="text"
              icon={<InfoCircleOutlined />}
              size="small"
            />
          </Tooltip>
          <Tooltip title="设置">
            <Button
              type="text"
              icon={<SettingOutlined />}
              size="small"
            />
          </Tooltip>
        </Space>
      }
    >
      <Spin spinning={loading}>
        {/* 搜索和过滤控件 */}
        <Space direction="vertical" style={{ width: '100%', marginBottom: 16 }}>
          <Search
            placeholder="搜索服务器节点..."
            allowClear
            onSearch={handleSearch}
            onChange={(e) => handleSearch(e.target.value)}
            prefix={<SearchOutlined />}
          />
          
          <Row gutter={8}>
            <Col span={12}>
              <Select
                value={selectedCategory}
                onChange={handleCategoryChange}
                style={{ width: '100%' }}
                size="small"
              >
                <Option value="all">全部分类</Option>
                <Option value={ServerNodeCategory.USER_SERVICE}>用户服务</Option>
                <Option value={ServerNodeCategory.DATA_SERVICE}>数据服务</Option>
                <Option value={ServerNodeCategory.FILE_SERVICE}>文件服务</Option>
                <Option value={ServerNodeCategory.AUTH_SERVICE}>认证授权</Option>
                <Option value={ServerNodeCategory.NOTIFICATION_SERVICE}>通知服务</Option>
                <Option value={ServerNodeCategory.MONITORING_SERVICE}>监控服务</Option>
                <Option value={ServerNodeCategory.PROJECT_MANAGEMENT}>项目管理</Option>
              </Select>
            </Col>
            <Col span={12}>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                共 {filteredNodes.length} 个节点
              </Text>
            </Col>
          </Row>
        </Space>

        <Divider style={{ margin: '12px 0' }} />

        {/* 节点分类面板 */}
        {nodesByCategory.size > 0 ? (
          <Collapse
            activeKey={expandedPanels}
            onChange={handlePanelChange}
            size="small"
            ghost
          >
            {Array.from(nodesByCategory.entries()).map(([category, nodes]) => (
              <Panel
                key={category}
                header={
                  <Space>
                    {getCategoryIcon(category)}
                    <Text strong>{getCategoryName(category)}</Text>
                    <Badge count={nodes.length} style={{ backgroundColor: '#1890ff' }} />
                  </Space>
                }
              >
                <List
                  dataSource={nodes}
                  renderItem={(node) => (
                    <DraggableServerNode
                      key={node.id}
                      node={node}
                      onNodeSelect={onNodeSelect}
                      onNodeAdd={onNodeAdd}
                    />
                  )}
                  size="small"
                />
              </Panel>
            ))}
          </Collapse>
        ) : (
          <Empty
            description="没有找到匹配的节点"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        )}
      </Spin>
    </Card>
  );
};

export default ServerSystemNodesPanel;
export type { ServerNodeItem, ServerSystemNodesPanelProps };
