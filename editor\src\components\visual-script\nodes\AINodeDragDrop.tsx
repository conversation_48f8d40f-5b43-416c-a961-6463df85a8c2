/**
 * AI节点拖拽组件
 * 为AI系统面板提供拖拽功能
 */

import React, { useState } from 'react';
import { List, Tag, Space, Button, Tooltip, Typography, Popover } from 'antd';
import {
  PlusOutlined,
  InfoCircleOutlined,
  EyeOutlined,
  DragOutlined,
  BrainOutlined,
  ExperimentOutlined,
  BuildOutlined,
  CloudOutlined,
  MessageOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { AINodeItem, AINodeCategory } from '../panels/AISystemNodesPanel';

const { Text } = Typography;

// 拖拽AI节点组件属性
interface DraggableAINodeProps {
  node: AINodeItem;
  onSelect?: (node: AINodeItem) => void;
  onAdd?: (nodeType: string) => void;
}

// 复杂度颜色映射
const COMPLEXITY_COLORS = {
  basic: '#52c41a',
  intermediate: '#faad14',
  advanced: '#f5222d'
};

// 复杂度标签映射
const COMPLEXITY_LABELS = {
  basic: '基础',
  intermediate: '中级',
  advanced: '高级'
};

// 分类图标映射
const CATEGORY_ICONS = {
  [AINodeCategory.DEEP_LEARNING]: <BrainOutlined />,
  [AINodeCategory.MACHINE_LEARNING]: <ExperimentOutlined />,
  [AINodeCategory.AI_TOOLS]: <BuildOutlined />,
  [AINodeCategory.AI_SERVICES]: <CloudOutlined />,
  [AINodeCategory.NATURAL_LANGUAGE_PROCESSING]: <MessageOutlined />,
  [AINodeCategory.MODEL_MANAGEMENT]: <SettingOutlined />
};

/**
 * 可拖拽的AI节点组件
 */
export const DraggableAINode: React.FC<DraggableAINodeProps> = ({
  node,
  onSelect,
  onAdd
}) => {
  const [isDragging, setIsDragging] = useState(false);

  // 处理拖拽开始
  const handleDragStart = (e: React.DragEvent) => {
    setIsDragging(true);
    e.dataTransfer.setData('application/json', JSON.stringify({
      type: 'ai-node',
      nodeType: node.type,
      nodeData: node
    }));
    e.dataTransfer.effectAllowed = 'copy';
  };

  // 处理拖拽结束
  const handleDragEnd = () => {
    setIsDragging(false);
  };

  // 处理节点选择
  const handleSelect = () => {
    onSelect?.(node);
  };

  // 处理添加节点
  const handleAdd = () => {
    onAdd?.(node.type);
  };

  // 节点详细信息内容
  const nodeDetailsContent = (
    <div style={{ maxWidth: 300 }}>
      <Space direction="vertical" size="small" style={{ width: '100%' }}>
        <div>
          <Text strong>节点类型：</Text>
          <Text code>{node.type}</Text>
        </div>
        <div>
          <Text strong>描述：</Text>
          <Text>{node.description}</Text>
        </div>
        <div>
          <Text strong>复杂度：</Text>
          <Tag color={COMPLEXITY_COLORS[node.complexity || 'basic']}>
            {COMPLEXITY_LABELS[node.complexity || 'basic']}
          </Tag>
        </div>
        {node.tags && node.tags.length > 0 && (
          <div>
            <Text strong>标签：</Text>
            <div style={{ marginTop: 4 }}>
              {node.tags.map(tag => (
                <Tag key={tag} size="small" style={{ marginBottom: 2 }}>
                  {tag}
                </Tag>
              ))}
            </div>
          </div>
        )}
        {node.inputs && node.inputs.length > 0 && (
          <div>
            <Text strong>输入：</Text>
            <div style={{ marginTop: 4 }}>
              {node.inputs.map((input, index) => (
                <div key={index} style={{ fontSize: '12px', marginBottom: 2 }}>
                  <Text code>{input.name}</Text>: {input.description}
                </div>
              ))}
            </div>
          </div>
        )}
        {node.outputs && node.outputs.length > 0 && (
          <div>
            <Text strong>输出：</Text>
            <div style={{ marginTop: 4 }}>
              {node.outputs.map((output, index) => (
                <div key={index} style={{ fontSize: '12px', marginBottom: 2 }}>
                  <Text code>{output.name}</Text>: {output.description}
                </div>
              ))}
            </div>
          </div>
        )}
      </Space>
    </div>
  );

  return (
    <List.Item
      style={{
        padding: '8px 12px',
        border: '1px solid #f0f0f0',
        borderRadius: '6px',
        marginBottom: '4px',
        cursor: 'grab',
        backgroundColor: isDragging ? '#f6ffed' : '#fff',
        transition: 'all 0.2s ease',
        opacity: isDragging ? 0.7 : 1
      }}
      draggable
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      onClick={handleSelect}
    >
      <div style={{ width: '100%' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <div style={{ flex: 1, minWidth: 0 }}>
            <Space size="small" style={{ marginBottom: 4 }}>
              <DragOutlined style={{ color: '#999', fontSize: '12px' }} />
              {CATEGORY_ICONS[node.category]}
              <Text strong style={{ fontSize: '13px' }}>
                {node.name}
              </Text>
              <Tag 
                size="small" 
                color={COMPLEXITY_COLORS[node.complexity || 'basic']}
                style={{ fontSize: '10px', lineHeight: '16px', height: '16px' }}
              >
                {COMPLEXITY_LABELS[node.complexity || 'basic']}
              </Tag>
            </Space>
            <div style={{ marginBottom: 4 }}>
              <Text 
                type="secondary" 
                style={{ 
                  fontSize: '11px', 
                  display: 'block',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap'
                }}
              >
                {node.description}
              </Text>
            </div>
            {node.tags && node.tags.length > 0 && (
              <div style={{ marginTop: 4 }}>
                {node.tags.slice(0, 3).map(tag => (
                  <Tag 
                    key={tag} 
                    size="small" 
                    style={{ 
                      fontSize: '10px', 
                      lineHeight: '14px', 
                      height: '14px',
                      marginBottom: 2,
                      marginRight: 4
                    }}
                  >
                    {tag}
                  </Tag>
                ))}
                {node.tags.length > 3 && (
                  <Text type="secondary" style={{ fontSize: '10px' }}>
                    +{node.tags.length - 3}
                  </Text>
                )}
              </div>
            )}
          </div>
          <div style={{ marginLeft: 8 }}>
            <Space size="small">
              <Popover
                content={nodeDetailsContent}
                title={
                  <Space>
                    {CATEGORY_ICONS[node.category]}
                    <Text strong>{node.name}</Text>
                  </Space>
                }
                placement="right"
                trigger="hover"
              >
                <Button
                  type="text"
                  size="small"
                  icon={<InfoCircleOutlined />}
                  style={{ fontSize: '12px', padding: '2px 4px' }}
                />
              </Popover>
              <Button
                type="text"
                size="small"
                icon={<EyeOutlined />}
                onClick={(e) => {
                  e.stopPropagation();
                  handleSelect();
                }}
                style={{ fontSize: '12px', padding: '2px 4px' }}
              />
              <Button
                type="primary"
                size="small"
                icon={<PlusOutlined />}
                onClick={(e) => {
                  e.stopPropagation();
                  handleAdd();
                }}
                style={{ fontSize: '12px', padding: '2px 4px' }}
              />
            </Space>
          </div>
        </div>
      </div>
    </List.Item>
  );
};

export default DraggableAINode;
